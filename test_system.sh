#!/bin/bash

# 勇远云联 Mesh VPN 系统测试脚本
# 测试完整的用户注册、登录、设备管理、分组管理流程

set -e

BASE_URL="http://localhost:8080"
USERNAME="testuser_$(date +%s)"
PASSWORD="testpass123"
EMAIL="${USERNAME}@example.com"

echo "=== 勇远云联 Mesh VPN 系统测试 ==="
echo "测试用户: $USERNAME"
echo "服务器: $BASE_URL"
echo

# 1. 测试健康检查
echo "1. 测试健康检查..."
curl -s "$BASE_URL/api/health" | jq .
echo

# 2. 测试版本信息
echo "2. 测试版本信息..."
curl -s "$BASE_URL/api/version" | jq .
echo

# 3. 测试用户注册
echo "3. 测试用户注册..."
REGISTER_RESPONSE=$(curl -s -X POST "$BASE_URL/api/auth/register" \
  -H "Content-Type: application/json" \
  -d "{\"username\":\"$USERNAME\",\"password\":\"$PASSWORD\",\"email\":\"$EMAIL\"}")
echo $REGISTER_RESPONSE | jq .
echo

# 4. 测试用户登录
echo "4. 测试用户登录..."
LOGIN_RESPONSE=$(curl -s -X POST "$BASE_URL/api/auth/login" \
  -H "Content-Type: application/json" \
  -d "{\"username\":\"$USERNAME\",\"password\":\"$PASSWORD\"}")
echo $LOGIN_RESPONSE | jq .

# 提取 Token
TOKEN=$(echo $LOGIN_RESPONSE | jq -r '.token')
if [ "$TOKEN" = "null" ] || [ -z "$TOKEN" ]; then
    echo "❌ 登录失败，无法获取 Token"
    exit 1
fi
echo "✅ 获取到 Token: ${TOKEN:0:20}..."
echo

# 5. 测试获取用户信息
echo "5. 测试获取用户信息..."
curl -s -X GET "$BASE_URL/api/user/profile" \
  -H "Authorization: Bearer $TOKEN" | jq .
echo

# 6. 测试获取用户统计
echo "6. 测试获取用户统计..."
curl -s -X GET "$BASE_URL/api/user/stats" \
  -H "Authorization: Bearer $TOKEN" | jq .
echo

# 7. 测试设备注册
echo "7. 测试设备注册..."
DEVICE_UUID=$(uuidgen)
DEVICE_RESPONSE=$(curl -s -X POST "$BASE_URL/api/device/register" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d "{\"device_uuid\":\"$DEVICE_UUID\",\"device_name\":\"test-device\",\"mac_address\":\"00:11:22:33:44:55\",\"lan_ip\":\"*************\",\"public_ip\":\"***********\"}")
echo $DEVICE_RESPONSE | jq .
echo

# 8. 测试获取设备列表
echo "8. 测试获取设备列表..."
curl -s -X GET "$BASE_URL/api/device/list" \
  -H "Authorization: Bearer $TOKEN" | jq .
echo

# 9. 测试设备心跳
echo "9. 测试设备心跳..."
HEARTBEAT_RESPONSE=$(curl -s -X POST "$BASE_URL/api/device/heartbeat" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d "{\"device_uuid\":\"$DEVICE_UUID\",\"device_name\":\"test-device\",\"mac_address\":\"00:11:22:33:44:55\",\"lan_ip\":\"*************\",\"public_ip\":\"***********\"}")
echo $HEARTBEAT_RESPONSE | jq .
echo

# 10. 测试创建分组
echo "10. 测试创建分组..."
GROUP_RESPONSE=$(curl -s -X POST "$BASE_URL/api/group" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d "{\"group_name\":\"test-group\",\"subnet\":\"********/24\"}")
echo $GROUP_RESPONSE | jq .

# 提取分组 ID
GROUP_ID=$(echo $GROUP_RESPONSE | jq -r '.group.id')
echo "✅ 创建分组成功，ID: $GROUP_ID"
echo

# 11. 测试获取分组列表
echo "11. 测试获取分组列表..."
curl -s -X GET "$BASE_URL/api/group/list" \
  -H "Authorization: Bearer $TOKEN" | jq .
echo

# 12. 测试添加设备到分组
echo "12. 测试添加设备到分组..."
curl -s -X POST "$BASE_URL/api/group/$GROUP_ID/devices" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d "{\"device_uuids\":[\"$DEVICE_UUID\"]}" | jq .
echo

# 13. 测试获取分组详情
echo "13. 测试获取分组详情..."
curl -s -X GET "$BASE_URL/api/group/$GROUP_ID" \
  -H "Authorization: Bearer $TOKEN" | jq .
echo

# 14. 测试获取分组统计
echo "14. 测试获取分组统计..."
curl -s -X GET "$BASE_URL/api/group/stats" \
  -H "Authorization: Bearer $TOKEN" | jq .
echo

# 15. 测试获取设备统计
echo "15. 测试获取设备统计..."
curl -s -X GET "$BASE_URL/api/device/stats" \
  -H "Authorization: Bearer $TOKEN" | jq .
echo

# 16. 测试获取订阅计划
echo "16. 测试获取订阅计划..."
curl -s -X GET "$BASE_URL/api/user/subscription/plans" \
  -H "Authorization: Bearer $TOKEN" | jq .
echo

echo "=== 测试完成 ==="
echo "✅ 所有核心功能测试通过"
echo "🎉 勇远云联 Mesh VPN 系统运行正常！"
