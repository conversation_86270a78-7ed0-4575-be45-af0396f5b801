# 开发决策记录

## [2025-06-14] 项目初始化

### 技术选型决策
- **后端语言**: Golang
  - 原因: 高性能、跨平台编译、丰富的网络库支持
  - 框架: Gin (HTTP) + Gorilla WebSocket
- **数据库**: PostgreSQL + Redis
  - PostgreSQL: 主数据存储，支持复杂查询和事务
  - Redis: 缓存和会话存储，提升性能
- **客户端**: Go (核心) + <PERSON><PERSON> (桌面GUI) + Flutter (移动端)
  - Go: 跨平台编译，统一核心逻辑
  - Tauri: 轻量级桌面应用，基于Web技术
  - Flutter: 跨平台移动应用开发
- **加密**: AES256 用于配置文件加密
- **认证**: JWT Token 认证机制

### 架构设计决策
- **分层架构**: Controller -> Service -> Model
  - 严格分层，禁止跨层调用
  - 每层职责明确，便于测试和维护
- **模块化设计**: 按功能模块划分包结构
  - server/internal/: 服务端内部模块
  - client/internal/: 客户端内部模块
  - 每个模块独立，降低耦合度
- **配置管理**: 统一配置文件格式
  - 服务端: YAML 格式
  - 客户端: JSON 格式
  - 支持环境变量覆盖

### 数据库设计决策
- **用户表 (users)**: 存储用户基本信息和订阅计划
- **设备表 (devices)**: 存储设备信息和WireGuard密钥
- **分组表 (groups)**: 支持设备分组管理
- **设备分组关联表 (device_groups)**: 多对多关系
- **服务器表 (servers)**: 支持多服务器部署

### API 设计决策
- **RESTful API**: 标准HTTP方法和状态码
- **WebSocket**: 用于实时心跳和配置推送
- **统一响应格式**: 
  ```json
  {
    "success": boolean,
    "message": string,
    "data": object
  }
  ```

### 安全设计决策
- **密码存储**: bcrypt 哈希加密
- **JWT Token**: 用于API认证，24小时过期
- **配置加密**: AES256 加密WireGuard配置文件
- **HTTPS**: 强制使用HTTPS通信
- **输入验证**: 所有API输入严格验证

### 部署设计决策
- **容器化**: Docker + Docker Compose
- **数据持久化**: 数据库数据卷挂载
- **配置外部化**: 环境变量和配置文件
- **多服务器**: 支持水平扩展和负载均衡

## [2025-06-14] 项目结构创建

### 目录结构设计
```
wireguard/
├── server/                 # 服务端代码
│   ├── cmd/               # 主程序入口
│   ├── internal/          # 内部模块
│   │   ├── api/          # API路由层
│   │   ├── user/         # 用户管理
│   │   ├── device/       # 设备管理
│   │   ├── group/        # 分组管理
│   │   ├── wireguard/    # WG配置生成
│   │   └── database/     # 数据库连接
│   ├── db/               # 数据库脚本
│   └── config/           # 配置文件
├── client/                # 客户端代码
│   ├── cli/              # CLI客户端
│   ├── gui/              # GUI客户端
│   │   ├── desktop/      # Tauri桌面端
│   │   └── mobile/       # Flutter移动端
│   └── internal/         # 内部模块
│       ├── auth/         # 认证模块
│       ├── heartbeat/    # 心跳模块
│       ├── wireguard/    # WG集成
│       └── nat/          # NAT穿透
├── relay/                # 中继服务器
├── deploy/               # 部署脚本
└── docs/                 # 文档
```

### 开发流程确定
1. **Phase 1**: 服务端核心功能 (用户、设备、分组、API)
2. **Phase 2**: CLI客户端基础功能 (认证、心跳、配置同步)
3. **Phase 3**: WireGuard集成和NAT穿透
4. **Phase 4**: GUI客户端开发
5. **Phase 5**: 高级功能 (自动更新、多服务器、中继)

### 质量保证机制
- **代码规范**: gofmt + golint + go vet
- **测试覆盖**: 单元测试 + 集成测试
- **文档同步**: 所有API和模块必须有文档
- **版本控制**: 语义化版本号
- **CI/CD**: 自动化构建和测试

## 待记录事项
- 具体实现过程中的技术难点和解决方案
- 性能优化措施
- 安全加固措施
- 部署和运维经验
- 用户反馈和改进建议
