package device

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"time"

	"github.com/yytcloud/meshvpn/client/internal/config"
)

/**
 * 设备管理客户端模块
 * 
 * 功能说明:
 * - 处理设备注册
 * - 管理设备信息
 * - 与服务端设备 API 交互
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2025-06-14
 */

/**
 * 设备客户端结构体
 */
type Client struct {
	config     *config.Config
	httpClient *http.Client
}

/**
 * 设备注册请求结构体
 */
type RegisterRequest struct {
	DeviceUUID string `json:"device_uuid"`
	DeviceName string `json:"device_name"`
	MacAddress string `json:"mac_address,omitempty"`
	LanIP      string `json:"lan_ip,omitempty"`
	PublicIP   string `json:"public_ip,omitempty"`
}

/**
 * 设备信息结构体
 */
type Device struct {
	ID                  uint   `json:"id"`
	UserID              uint   `json:"user_id"`
	DeviceUUID          string `json:"device_uuid"`
	DeviceName          string `json:"device_name"`
	MacAddress          string `json:"mac_address"`
	LanIP               string `json:"lan_ip"`
	PublicIP            string `json:"public_ip"`
	WireguardPublicKey  string `json:"wireguard_public_key"`
	InterfaceIP         string `json:"interface_ip"`
	Status              string `json:"status"`
	CreatedAt           string `json:"created_at"`
}

/**
 * 设备注册响应结构体
 */
type RegisterResponse struct {
	Success bool    `json:"success"`
	Message string  `json:"message"`
	Device  *Device `json:"device,omitempty"`
}

/**
 * 通用响应结构体
 */
type Response struct {
	Success bool        `json:"success"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

/**
 * 创建设备客户端
 * 
 * @param config 客户端配置
 * @return *Client 设备客户端实例
 */
func NewClient(config *config.Config) *Client {
	return &Client{
		config: config,
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

/**
 * 注册设备
 * 
 * @param macAddress MAC 地址
 * @param lanIP 局域网 IP
 * @param publicIP 公网 IP
 * @return *Device 设备信息
 * @return error 错误信息
 */
func (c *Client) RegisterDevice(macAddress, lanIP, publicIP string) (*Device, error) {
	req := RegisterRequest{
		DeviceUUID: c.config.DeviceUUID,
		DeviceName: c.config.DeviceName,
		MacAddress: macAddress,
		LanIP:      lanIP,
		PublicIP:   publicIP,
	}

	var response RegisterResponse
	err := c.makeRequest("POST", "/api/device/register", req, &response)
	if err != nil {
		return nil, fmt.Errorf("设备注册请求失败: %w", err)
	}

	if !response.Success {
		return nil, fmt.Errorf("设备注册失败: %s", response.Message)
	}

	return response.Device, nil
}

/**
 * 获取设备信息
 * 
 * @param deviceUUID 设备 UUID
 * @return *Device 设备信息
 * @return error 错误信息
 */
func (c *Client) GetDevice(deviceUUID string) (*Device, error) {
	var response Response
	err := c.makeRequest("GET", "/api/device/"+deviceUUID, nil, &response)
	if err != nil {
		return nil, fmt.Errorf("获取设备信息失败: %w", err)
	}

	if !response.Success {
		return nil, fmt.Errorf("获取设备信息失败: %s", response.Message)
	}

	// 解析设备数据
	deviceData, err := json.Marshal(response.Data)
	if err != nil {
		return nil, fmt.Errorf("解析设备数据失败: %w", err)
	}

	var device Device
	err = json.Unmarshal(deviceData, &device)
	if err != nil {
		return nil, fmt.Errorf("解析设备信息失败: %w", err)
	}

	return &device, nil
}

/**
 * 获取设备列表
 * 
 * @return []Device 设备列表
 * @return error 错误信息
 */
func (c *Client) GetDeviceList() ([]Device, error) {
	var response struct {
		Success bool     `json:"success"`
		Message string   `json:"message"`
		Devices []Device `json:"devices"`
		Total   int64    `json:"total"`
	}

	err := c.makeRequest("GET", "/api/device/list", nil, &response)
	if err != nil {
		return nil, fmt.Errorf("获取设备列表失败: %w", err)
	}

	if !response.Success {
		return nil, fmt.Errorf("获取设备列表失败: %s", response.Message)
	}

	return response.Devices, nil
}

/**
 * 删除设备
 * 
 * @param deviceUUID 设备 UUID
 * @return error 错误信息
 */
func (c *Client) DeleteDevice(deviceUUID string) error {
	var response Response
	err := c.makeRequest("DELETE", "/api/device/"+deviceUUID, nil, &response)
	if err != nil {
		return fmt.Errorf("删除设备失败: %w", err)
	}

	if !response.Success {
		return fmt.Errorf("删除设备失败: %s", response.Message)
	}

	return nil
}

/**
 * 发送 HTTP 请求
 * 
 * @param method HTTP 方法
 * @param path API 路径
 * @param requestBody 请求体
 * @param responseBody 响应体
 * @return error 错误信息
 */
func (c *Client) makeRequest(method, path string, requestBody interface{}, responseBody interface{}) error {
	// 序列化请求体
	var reqData []byte
	var err error
	if requestBody != nil {
		reqData, err = json.Marshal(requestBody)
		if err != nil {
			return fmt.Errorf("序列化请求体失败: %w", err)
		}
	}

	// 尝试所有服务器端点
	var lastErr error
	for _, endpoint := range c.config.ServerEndpoints {
		url := endpoint + path

		// 创建请求
		req, err := http.NewRequest(method, url, bytes.NewBuffer(reqData))
		if err != nil {
			lastErr = err
			continue
		}

		req.Header.Set("Content-Type", "application/json")
		if c.config.Auth.Token != "" {
			req.Header.Set("Authorization", "Bearer "+c.config.Auth.Token)
		}

		// 发送请求
		resp, err := c.httpClient.Do(req)
		if err != nil {
			lastErr = err
			continue
		}
		defer resp.Body.Close()

		// 读取响应
		respData, err := ioutil.ReadAll(resp.Body)
		if err != nil {
			lastErr = err
			continue
		}

		// 解析响应
		if responseBody != nil {
			err = json.Unmarshal(respData, responseBody)
			if err != nil {
				lastErr = fmt.Errorf("解析响应失败: %w", err)
				continue
			}
		}

		return nil
	}

	return fmt.Errorf("所有服务器端点都无法连接，最后错误: %w", lastErr)
}
