package config

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"os"
	"path/filepath"

	"github.com/google/uuid"
)

/**
 * 客户端配置管理模块
 * 
 * 功能说明:
 * - 管理客户端配置文件
 * - 提供配置加载和保存功能
 * - 生成设备唯一标识
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2025-06-14
 */

/**
 * 客户端配置结构体
 */
type Config struct {
	ServerEndpoints []string `json:"server_endpoints"`
	DeviceName      string   `json:"device_name"`
	DeviceUUID      string   `json:"device_uuid"`
	Groups          []Group  `json:"groups"`
	Auth            Auth     `json:"auth"`
	Heartbeat       Heartbeat `json:"heartbeat"`
	WireGuard       WireGuard `json:"wireguard"`
}

/**
 * 分组配置结构体
 */
type Group struct {
	GroupName string   `json:"group_name"`
	Members   []string `json:"members"`
}

/**
 * 认证配置结构体
 */
type Auth struct {
	Username string `json:"username"`
	Token    string `json:"token"`
	UserID   uint   `json:"user_id"`
}

/**
 * 心跳配置结构体
 */
type Heartbeat struct {
	Interval int  `json:"interval"` // 心跳间隔（秒）
	Enabled  bool `json:"enabled"`  // 是否启用心跳
}

/**
 * WireGuard 配置结构体
 */
type WireGuard struct {
	Interface   string `json:"interface"`    // 接口名称
	Port        int    `json:"port"`         // 监听端口
	ConfigPath  string `json:"config_path"`  // 配置文件路径
	AutoConnect bool   `json:"auto_connect"` // 自动连接
}

/**
 * 加载配置文件
 * 
 * @param configPath 配置文件路径
 * @return *Config 配置对象
 * @return error 错误信息
 */
func LoadConfig(configPath string) (*Config, error) {
	// 检查文件是否存在
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		return nil, fmt.Errorf("配置文件不存在: %s", configPath)
	}

	// 读取文件内容
	data, err := ioutil.ReadFile(configPath)
	if err != nil {
		return nil, fmt.Errorf("读取配置文件失败: %w", err)
	}

	// 解析 JSON
	var config Config
	if err := json.Unmarshal(data, &config); err != nil {
		return nil, fmt.Errorf("解析配置文件失败: %w", err)
	}

	// 验证配置
	if err := validateConfig(&config); err != nil {
		return nil, fmt.Errorf("配置验证失败: %w", err)
	}

	return &config, nil
}

/**
 * 保存配置文件
 * 
 * @param configPath 配置文件路径
 * @param config 配置对象
 * @return error 错误信息
 */
func SaveConfig(configPath string, config *Config) error {
	// 创建目录
	dir := filepath.Dir(configPath)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return fmt.Errorf("创建配置目录失败: %w", err)
	}

	// 序列化为 JSON
	data, err := json.MarshalIndent(config, "", "  ")
	if err != nil {
		return fmt.Errorf("序列化配置失败: %w", err)
	}

	// 写入文件
	if err := ioutil.WriteFile(configPath, data, 0644); err != nil {
		return fmt.Errorf("写入配置文件失败: %w", err)
	}

	return nil
}

/**
 * 获取默认配置
 * 
 * @return *Config 默认配置对象
 */
func GetDefaultConfig() *Config {
	// 生成设备 UUID
	deviceUUID := uuid.New().String()
	
	// 获取主机名作为设备名称
	hostname, err := os.Hostname()
	if err != nil {
		hostname = "unknown-device"
	}

	return &Config{
		ServerEndpoints: []string{
			"http://localhost:8080",
		},
		DeviceName: hostname,
		DeviceUUID: deviceUUID,
		Groups: []Group{
			{
				GroupName: "default",
				Members:   []string{hostname},
			},
		},
		Auth: Auth{
			Username: "",
			Token:    "",
			UserID:   0,
		},
		Heartbeat: Heartbeat{
			Interval: 5,
			Enabled:  true,
		},
		WireGuard: WireGuard{
			Interface:   "wg0",
			Port:        51820,
			ConfigPath:  "/etc/wireguard/wg0.conf",
			AutoConnect: true,
		},
	}
}

/**
 * 创建示例配置文件
 * 
 * @param configPath 配置文件路径
 * @return error 错误信息
 */
func CreateExampleConfig(configPath string) error {
	config := GetDefaultConfig()
	return SaveConfig(configPath, config)
}

/**
 * 验证配置
 * 
 * @param config 配置对象
 * @return error 错误信息
 */
func validateConfig(config *Config) error {
	// 验证服务器端点
	if len(config.ServerEndpoints) == 0 {
		return fmt.Errorf("至少需要配置一个服务器端点")
	}

	// 验证设备名称
	if config.DeviceName == "" {
		return fmt.Errorf("设备名称不能为空")
	}

	// 验证设备 UUID
	if config.DeviceUUID == "" {
		// 自动生成 UUID
		config.DeviceUUID = uuid.New().String()
	}

	// 验证心跳间隔
	if config.Heartbeat.Interval <= 0 {
		config.Heartbeat.Interval = 5
	}

	// 验证 WireGuard 端口
	if config.WireGuard.Port <= 0 || config.WireGuard.Port > 65535 {
		config.WireGuard.Port = 51820
	}

	return nil
}

/**
 * 获取配置文件默认路径
 * 
 * @return string 默认配置文件路径
 */
func GetDefaultConfigPath() string {
	// 尝试获取用户主目录
	homeDir, err := os.UserHomeDir()
	if err != nil {
		return "./config.json"
	}

	return filepath.Join(homeDir, ".meshvpn", "config.json")
}

/**
 * 检查配置文件是否存在
 * 
 * @param configPath 配置文件路径
 * @return bool 是否存在
 */
func ConfigExists(configPath string) bool {
	_, err := os.Stat(configPath)
	return !os.IsNotExist(err)
}

/**
 * 获取设备信息
 * 
 * @param config 配置对象
 * @return map[string]interface{} 设备信息
 */
func GetDeviceInfo(config *Config) map[string]interface{} {
	return map[string]interface{}{
		"device_uuid": config.DeviceUUID,
		"device_name": config.DeviceName,
		"groups":      config.Groups,
	}
}
