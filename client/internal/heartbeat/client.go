package heartbeat

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"log"
	"net"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/yytcloud/meshvpn/client/internal/config"
)

/**
 * 心跳客户端模块
 * 
 * 功能说明:
 * - 定时向服务端发送心跳
 * - 获取网络接口信息
 * - 处理配置更新
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2025-06-14
 */

/**
 * 心跳客户端结构体
 */
type Client struct {
	config     *config.Config
	httpClient *http.Client
	stopChan   chan bool
	doneChan   chan bool
}

/**
 * 心跳请求结构体
 */
type HeartbeatRequest struct {
	DeviceUUID string `json:"device_uuid"`
	DeviceName string `json:"device_name"`
	MacAddress string `json:"mac_address"`
	LanIP      string `json:"lan_ip"`
	PublicIP   string `json:"public_ip"`
	Route1     string `json:"route1,omitempty"`
	Route2     string `json:"route2,omitempty"`
}

/**
 * 对等节点信息结构体
 */
type PeerInfo struct {
	DeviceUUID    string `json:"device_uuid"`
	DeviceName    string `json:"device_name"`
	PublicKey     string `json:"public_key"`
	InterfaceIP   string `json:"interface_ip"`
	Endpoint      string `json:"endpoint"`
	AllowedIPs    string `json:"allowed_ips"`
	LastHandshake string `json:"last_handshake"`
}

/**
 * 心跳响应结构体
 */
type HeartbeatResponse struct {
	Success       bool       `json:"success"`
	Message       string     `json:"message"`
	ConfigUpdated bool       `json:"config_updated"`
	Peers         []PeerInfo `json:"peers,omitempty"`
}

/**
 * 创建心跳客户端
 * 
 * @param config 客户端配置
 * @return *Client 心跳客户端实例
 */
func NewClient(config *config.Config) *Client {
	return &Client{
		config: config,
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
		stopChan: make(chan bool),
		doneChan: make(chan bool),
	}
}

/**
 * 启动心跳服务
 * 
 * @return error 错误信息
 */
func (c *Client) Start() error {
	if !c.config.Heartbeat.Enabled {
		log.Println("心跳服务已禁用")
		return nil
	}

	log.Printf("启动心跳服务，间隔: %d 秒", c.config.Heartbeat.Interval)

	// 立即发送一次心跳
	err := c.sendHeartbeat()
	if err != nil {
		log.Printf("初始心跳发送失败: %v", err)
	}

	// 启动定时心跳
	go c.heartbeatLoop()

	return nil
}

/**
 * 停止心跳服务
 */
func (c *Client) Stop() {
	log.Println("停止心跳服务...")
	close(c.stopChan)
	<-c.doneChan
	log.Println("心跳服务已停止")
}

/**
 * 等待服务结束
 */
func (c *Client) Wait() {
	// 监听系统信号
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	select {
	case sig := <-sigChan:
		log.Printf("收到信号: %v", sig)
		c.Stop()
	case <-c.doneChan:
		log.Println("心跳服务正常结束")
	}
}

/**
 * 心跳循环
 */
func (c *Client) heartbeatLoop() {
	defer close(c.doneChan)

	ticker := time.NewTicker(time.Duration(c.config.Heartbeat.Interval) * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			err := c.sendHeartbeat()
			if err != nil {
				log.Printf("心跳发送失败: %v", err)
			}
		case <-c.stopChan:
			return
		}
	}
}

/**
 * 发送心跳
 * 
 * @return error 错误信息
 */
func (c *Client) sendHeartbeat() error {
	// 获取网络信息
	macAddress, err := c.getMacAddress()
	if err != nil {
		log.Printf("获取 MAC 地址失败: %v", err)
		macAddress = ""
	}

	lanIP, err := c.getLanIP()
	if err != nil {
		log.Printf("获取局域网 IP 失败: %v", err)
		lanIP = ""
	}

	publicIP, err := c.getPublicIP()
	if err != nil {
		log.Printf("获取公网 IP 失败: %v", err)
		publicIP = ""
	}

	// 构建心跳请求
	req := HeartbeatRequest{
		DeviceUUID: c.config.DeviceUUID,
		DeviceName: c.config.DeviceName,
		MacAddress: macAddress,
		LanIP:      lanIP,
		PublicIP:   publicIP,
	}

	// 发送请求
	var response HeartbeatResponse
	err = c.makeRequest("POST", "/api/device/heartbeat", req, &response)
	if err != nil {
		return fmt.Errorf("心跳请求失败: %w", err)
	}

	if !response.Success {
		return fmt.Errorf("心跳失败: %s", response.Message)
	}

	log.Printf("心跳成功，对等节点数量: %d", len(response.Peers))

	// 处理配置更新
	if response.ConfigUpdated {
		log.Println("检测到配置更新")
		// TODO: 实现配置更新逻辑
	}

	// 处理对等节点信息
	if len(response.Peers) > 0 {
		c.processPeers(response.Peers)
	}

	return nil
}

/**
 * 处理对等节点信息
 * 
 * @param peers 对等节点列表
 */
func (c *Client) processPeers(peers []PeerInfo) {
	log.Printf("处理 %d 个对等节点", len(peers))
	
	for _, peer := range peers {
		log.Printf("对等节点: %s (%s) - %s", peer.DeviceName, peer.DeviceUUID, peer.Endpoint)
	}

	// TODO: 实现 WireGuard 配置更新逻辑
}

/**
 * 获取 MAC 地址
 * 
 * @return string MAC 地址
 * @return error 错误信息
 */
func (c *Client) getMacAddress() (string, error) {
	interfaces, err := net.Interfaces()
	if err != nil {
		return "", err
	}

	for _, iface := range interfaces {
		// 跳过回环接口和虚拟接口
		if iface.Flags&net.FlagLoopback != 0 || iface.Flags&net.FlagUp == 0 {
			continue
		}

		// 返回第一个有效的 MAC 地址
		if len(iface.HardwareAddr) > 0 {
			return iface.HardwareAddr.String(), nil
		}
	}

	return "", fmt.Errorf("未找到有效的网络接口")
}

/**
 * 获取局域网 IP
 * 
 * @return string 局域网 IP
 * @return error 错误信息
 */
func (c *Client) getLanIP() (string, error) {
	conn, err := net.Dial("udp", "*******:80")
	if err != nil {
		return "", err
	}
	defer conn.Close()

	localAddr := conn.LocalAddr().(*net.UDPAddr)
	return localAddr.IP.String(), nil
}

/**
 * 获取公网 IP
 * 
 * @return string 公网 IP
 * @return error 错误信息
 */
func (c *Client) getPublicIP() (string, error) {
	// 使用多个服务获取公网 IP
	services := []string{
		"https://api.ipify.org",
		"https://icanhazip.com",
		"https://ipinfo.io/ip",
	}

	for _, service := range services {
		resp, err := c.httpClient.Get(service)
		if err != nil {
			continue
		}
		defer resp.Body.Close()

		if resp.StatusCode == http.StatusOK {
			body, err := ioutil.ReadAll(resp.Body)
			if err != nil {
				continue
			}

			ip := string(bytes.TrimSpace(body))
			if net.ParseIP(ip) != nil {
				return ip, nil
			}
		}
	}

	return "", fmt.Errorf("无法获取公网 IP")
}

/**
 * 发送 HTTP 请求
 * 
 * @param method HTTP 方法
 * @param path API 路径
 * @param requestBody 请求体
 * @param responseBody 响应体
 * @return error 错误信息
 */
func (c *Client) makeRequest(method, path string, requestBody interface{}, responseBody interface{}) error {
	// 序列化请求体
	reqData, err := json.Marshal(requestBody)
	if err != nil {
		return fmt.Errorf("序列化请求体失败: %w", err)
	}

	// 尝试所有服务器端点
	var lastErr error
	for _, endpoint := range c.config.ServerEndpoints {
		url := endpoint + path

		// 创建请求
		req, err := http.NewRequest(method, url, bytes.NewBuffer(reqData))
		if err != nil {
			lastErr = err
			continue
		}

		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("Authorization", "Bearer "+c.config.Auth.Token)

		// 发送请求
		resp, err := c.httpClient.Do(req)
		if err != nil {
			lastErr = err
			continue
		}
		defer resp.Body.Close()

		// 读取响应
		respData, err := ioutil.ReadAll(resp.Body)
		if err != nil {
			lastErr = err
			continue
		}

		// 解析响应
		if responseBody != nil {
			err = json.Unmarshal(respData, responseBody)
			if err != nil {
				lastErr = fmt.Errorf("解析响应失败: %w", err)
				continue
			}
		}

		return nil
	}

	return fmt.Errorf("所有服务器端点都无法连接，最后错误: %w", lastErr)
}
