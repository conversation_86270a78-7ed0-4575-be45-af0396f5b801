package auth

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"time"
)

/**
 * 客户端认证模块
 * 
 * 功能说明:
 * - 处理用户注册和登录
 * - 管理 JWT Token
 * - 与服务端认证 API 交互
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2025-06-14
 */

/**
 * 认证客户端结构体
 */
type Client struct {
	serverEndpoints []string
	httpClient      *http.Client
	currentEndpoint string
}

/**
 * 用户信息结构体
 */
type User struct {
	ID               uint   `json:"id"`
	Username         string `json:"username"`
	Email            string `json:"email"`
	Phone            string `json:"phone"`
	SubscriptionPlan string `json:"subscription_plan"`
	DeviceLimit      int    `json:"device_limit"`
}

/**
 * 注册请求结构体
 */
type RegisterRequest struct {
	Username string `json:"username"`
	Password string `json:"password"`
	Email    string `json:"email"`
	Phone    string `json:"phone,omitempty"`
}

/**
 * 登录请求结构体
 */
type LoginRequest struct {
	Username string `json:"username"`
	Password string `json:"password"`
}

/**
 * 登录响应结构体
 */
type LoginResponse struct {
	Success   bool   `json:"success"`
	Message   string `json:"message"`
	Token     string `json:"token"`
	ExpiresIn int64  `json:"expires_in"`
	User      *User  `json:"user"`
}

/**
 * 通用响应结构体
 */
type Response struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
}

/**
 * 创建认证客户端
 * 
 * @param serverEndpoints 服务器端点列表
 * @return *Client 认证客户端实例
 */
func NewClient(serverEndpoints []string) *Client {
	return &Client{
		serverEndpoints: serverEndpoints,
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
		currentEndpoint: serverEndpoints[0], // 默认使用第一个端点
	}
}

/**
 * 用户注册
 * 
 * @param username 用户名
 * @param password 密码
 * @param email 邮箱
 * @param phone 手机号
 * @return error 错误信息
 */
func (c *Client) Register(username, password, email, phone string) error {
	req := RegisterRequest{
		Username: username,
		Password: password,
		Email:    email,
		Phone:    phone,
	}

	var response Response
	err := c.makeRequest("POST", "/api/auth/register", req, &response)
	if err != nil {
		return fmt.Errorf("注册请求失败: %w", err)
	}

	if !response.Success {
		return fmt.Errorf("注册失败: %s", response.Message)
	}

	return nil
}

/**
 * 用户登录
 * 
 * @param username 用户名
 * @param password 密码
 * @return string JWT Token
 * @return *User 用户信息
 * @return error 错误信息
 */
func (c *Client) Login(username, password string) (string, *User, error) {
	req := LoginRequest{
		Username: username,
		Password: password,
	}

	var response LoginResponse
	err := c.makeRequest("POST", "/api/auth/login", req, &response)
	if err != nil {
		return "", nil, fmt.Errorf("登录请求失败: %w", err)
	}

	if !response.Success {
		return "", nil, fmt.Errorf("登录失败: %s", response.Message)
	}

	return response.Token, response.User, nil
}

/**
 * 验证 Token 有效性
 * 
 * @param token JWT Token
 * @return bool 是否有效
 */
func (c *Client) ValidateToken(token string) bool {
	req, err := http.NewRequest("GET", c.currentEndpoint+"/api/user/profile", nil)
	if err != nil {
		return false
	}

	req.Header.Set("Authorization", "Bearer "+token)
	req.Header.Set("Content-Type", "application/json")

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return false
	}
	defer resp.Body.Close()

	return resp.StatusCode == http.StatusOK
}

/**
 * 刷新 Token
 * 
 * @param token 当前 Token
 * @return string 新的 Token
 * @return error 错误信息
 */
func (c *Client) RefreshToken(token string) (string, error) {
	req := map[string]string{
		"token": token,
	}

	var response LoginResponse
	err := c.makeRequest("POST", "/api/auth/refresh", req, &response)
	if err != nil {
		return "", fmt.Errorf("刷新 Token 失败: %w", err)
	}

	if !response.Success {
		return "", fmt.Errorf("刷新 Token 失败: %s", response.Message)
	}

	return response.Token, nil
}

/**
 * 发送 HTTP 请求
 * 
 * @param method HTTP 方法
 * @param path API 路径
 * @param requestBody 请求体
 * @param responseBody 响应体
 * @return error 错误信息
 */
func (c *Client) makeRequest(method, path string, requestBody interface{}, responseBody interface{}) error {
	// 序列化请求体
	var reqData []byte
	var err error
	if requestBody != nil {
		reqData, err = json.Marshal(requestBody)
		if err != nil {
			return fmt.Errorf("序列化请求体失败: %w", err)
		}
	}

	// 尝试所有服务器端点
	var lastErr error
	for _, endpoint := range c.serverEndpoints {
		url := endpoint + path
		
		// 创建请求
		req, err := http.NewRequest(method, url, bytes.NewBuffer(reqData))
		if err != nil {
			lastErr = err
			continue
		}

		req.Header.Set("Content-Type", "application/json")

		// 发送请求
		resp, err := c.httpClient.Do(req)
		if err != nil {
			lastErr = err
			continue
		}
		defer resp.Body.Close()

		// 读取响应
		respData, err := ioutil.ReadAll(resp.Body)
		if err != nil {
			lastErr = err
			continue
		}

		// 解析响应
		if responseBody != nil {
			err = json.Unmarshal(respData, responseBody)
			if err != nil {
				lastErr = fmt.Errorf("解析响应失败: %w", err)
				continue
			}
		}

		// 更新当前端点
		c.currentEndpoint = endpoint
		return nil
	}

	return fmt.Errorf("所有服务器端点都无法连接，最后错误: %w", lastErr)
}

/**
 * 获取当前使用的服务器端点
 * 
 * @return string 当前端点
 */
func (c *Client) GetCurrentEndpoint() string {
	return c.currentEndpoint
}

/**
 * 设置 HTTP 客户端超时时间
 * 
 * @param timeout 超时时间
 */
func (c *Client) SetTimeout(timeout time.Duration) {
	c.httpClient.Timeout = timeout
}
