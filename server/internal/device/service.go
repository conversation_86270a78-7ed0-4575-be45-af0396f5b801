package device

import (
	"errors"
	"fmt"
	"time"

	"golang.zx2c4.com/wireguard/wgctrl/wgtypes"
	"gorm.io/gorm"

	"github.com/yytcloud/meshvpn/server/internal/database"
)

/**
 * 设备管理模块 - 服务层
 * 
 * 功能说明:
 * - 处理设备注册、心跳、状态管理等业务逻辑
 * - 管理 WireGuard 密钥生成和分配
 * - 提供设备信息查询和更新功能
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2025-06-14
 */

// 错误定义
var (
	ErrDeviceNotFound    = errors.New("设备不存在")
	ErrDeviceExists      = errors.New("设备已存在")
	ErrInvalidDeviceUUID = errors.New("无效的设备UUID")
	ErrDeviceLimitExceeded = errors.New("设备数量超过限制")
	ErrKeyGenerationFailed = errors.New("密钥生成失败")
)

// IP 分配相关常量
const (
	BaseSubnet = "10.0.0.0/16" // 基础子网
	SubnetMask = 24            // 子网掩码
)

/**
 * 设备服务结构体
 */
type Service struct {
	db *gorm.DB
}

/**
 * 创建设备服务实例
 * 
 * @return *Service 设备服务实例
 */
func NewService() *Service {
	return &Service{
		db: database.GetDB(),
	}
}

/**
 * 注册设备
 * 
 * @param userID 用户ID
 * @param req 注册请求
 * @return *Device 创建的设备信息
 * @return error 错误信息
 */
func (s *Service) RegisterDevice(userID uint, req *RegisterRequest) (*Device, error) {
	// 检查设备是否已存在
	var existingDevice Device
	if err := s.db.Where("device_uuid = ?", req.DeviceUUID).First(&existingDevice).Error; err == nil {
		return nil, ErrDeviceExists
	}

	// 检查用户设备数量限制
	if err := s.checkDeviceLimit(userID); err != nil {
		return nil, err
	}

	// 生成 WireGuard 密钥对
	privateKey, publicKey, err := s.generateWireGuardKeys()
	if err != nil {
		return nil, err
	}

	// 分配接口IP
	interfaceIP, err := s.allocateInterfaceIP(userID)
	if err != nil {
		return nil, err
	}

	// 创建设备
	device := &Device{
		UserID:              userID,
		DeviceUUID:          req.DeviceUUID,
		DeviceName:          req.DeviceName,
		MacAddress:          req.MacAddress,
		LanIP:               req.LanIP,
		PublicIP:            req.PublicIP,
		WireguardPrivateKey: privateKey,
		WireguardPublicKey:  publicKey,
		InterfaceIP:         interfaceIP,
		Status:              DeviceStatusOffline,
		CreatedAt:           time.Now(),
		UpdatedAt:           time.Now(),
	}

	if err := s.db.Create(device).Error; err != nil {
		return nil, fmt.Errorf("创建设备失败: %w", err)
	}

	return device, nil
}

/**
 * 处理设备心跳
 * 
 * @param req 心跳请求
 * @return *HeartbeatResponse 心跳响应
 * @return error 错误信息
 */
func (s *Service) ProcessHeartbeat(req *HeartbeatRequest) (*HeartbeatResponse, error) {
	var device Device
	
	// 查找设备
	if err := s.db.Where("device_uuid = ?", req.DeviceUUID).First(&device).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, ErrDeviceNotFound
		}
		return nil, fmt.Errorf("查询设备失败: %w", err)
	}

	// 更新设备信息
	updates := map[string]interface{}{
		"device_name":     req.DeviceName,
		"last_heartbeat": time.Now(),
		"status":         DeviceStatusOnline,
		"updated_at":     time.Now(),
	}

	if req.MacAddress != "" {
		updates["mac_address"] = req.MacAddress
	}
	if req.LanIP != "" {
		updates["lan_ip"] = req.LanIP
	}
	if req.PublicIP != "" {
		updates["public_ip"] = req.PublicIP
	}

	if err := s.db.Model(&device).Updates(updates).Error; err != nil {
		return nil, fmt.Errorf("更新设备信息失败: %w", err)
	}

	// 获取对等节点信息
	peers, err := s.getPeerList(device.UserID, device.ID)
	if err != nil {
		return nil, fmt.Errorf("获取对等节点失败: %w", err)
	}

	response := &HeartbeatResponse{
		Success:       true,
		Message:       "心跳接收成功",
		ConfigUpdated: false, // TODO: 实现配置变更检测
		Peers:         peers,
	}

	return response, nil
}

/**
 * 获取用户设备列表
 * 
 * @param userID 用户ID
 * @param limit 限制数量
 * @param offset 偏移量
 * @return []Device 设备列表
 * @return int64 总数量
 * @return error 错误信息
 */
func (s *Service) GetUserDevices(userID uint, limit, offset int) ([]Device, int64, error) {
	var devices []Device
	var total int64

	// 获取总数
	if err := s.db.Model(&Device{}).Where("user_id = ?", userID).Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("查询设备总数失败: %w", err)
	}

	// 获取设备列表
	query := s.db.Where("user_id = ?", userID).Order("created_at DESC")
	if limit > 0 {
		query = query.Limit(limit)
	}
	if offset > 0 {
		query = query.Offset(offset)
	}

	if err := query.Find(&devices).Error; err != nil {
		return nil, 0, fmt.Errorf("查询设备列表失败: %w", err)
	}

	return devices, total, nil
}

/**
 * 根据设备UUID获取设备信息
 * 
 * @param deviceUUID 设备UUID
 * @return *Device 设备信息
 * @return error 错误信息
 */
func (s *Service) GetDeviceByUUID(deviceUUID string) (*Device, error) {
	var device Device
	
	if err := s.db.Where("device_uuid = ?", deviceUUID).First(&device).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, ErrDeviceNotFound
		}
		return nil, fmt.Errorf("查询设备失败: %w", err)
	}

	return &device, nil
}

/**
 * 更新设备信息
 * 
 * @param deviceUUID 设备UUID
 * @param req 更新请求
 * @return error 错误信息
 */
func (s *Service) UpdateDevice(deviceUUID string, req *UpdateRequest) error {
	updates := map[string]interface{}{
		"updated_at": time.Now(),
	}

	if req.DeviceName != "" {
		updates["device_name"] = req.DeviceName
	}
	if req.MacAddress != "" {
		updates["mac_address"] = req.MacAddress
	}
	if req.LanIP != "" {
		updates["lan_ip"] = req.LanIP
	}
	if req.PublicIP != "" {
		updates["public_ip"] = req.PublicIP
	}

	result := s.db.Model(&Device{}).Where("device_uuid = ?", deviceUUID).Updates(updates)
	if result.Error != nil {
		return fmt.Errorf("更新设备失败: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return ErrDeviceNotFound
	}

	return nil
}

/**
 * 删除设备
 * 
 * @param deviceUUID 设备UUID
 * @return error 错误信息
 */
func (s *Service) DeleteDevice(deviceUUID string) error {
	result := s.db.Where("device_uuid = ?", deviceUUID).Delete(&Device{})
	
	if result.Error != nil {
		return fmt.Errorf("删除设备失败: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return ErrDeviceNotFound
	}

	return nil
}

/**
 * 获取设备统计信息
 * 
 * @param userID 用户ID
 * @return *DeviceStats 设备统计信息
 * @return error 错误信息
 */
func (s *Service) GetDeviceStats(userID uint) (*DeviceStats, error) {
	var stats DeviceStats

	// 获取总设备数
	if err := s.db.Model(&Device{}).Where("user_id = ?", userID).Count(&stats.TotalDevices).Error; err != nil {
		return nil, fmt.Errorf("查询总设备数失败: %w", err)
	}

	// 获取在线设备数
	if err := s.db.Model(&Device{}).Where("user_id = ? AND status = ?", userID, DeviceStatusOnline).Count(&stats.OnlineDevices).Error; err != nil {
		return nil, fmt.Errorf("查询在线设备数失败: %w", err)
	}

	stats.OfflineDevices = stats.TotalDevices - stats.OnlineDevices

	return &stats, nil
}

/**
 * 检查设备数量限制
 */
func (s *Service) checkDeviceLimit(userID uint) error {
	// 查询用户信息
	var user struct {
		DeviceLimit int `json:"device_limit"`
	}
	
	if err := s.db.Table("users").Select("device_limit").Where("id = ?", userID).First(&user).Error; err != nil {
		return fmt.Errorf("查询用户信息失败: %w", err)
	}

	// -1 表示无限制
	if user.DeviceLimit == -1 {
		return nil
	}

	// 查询当前设备数量
	var deviceCount int64
	if err := s.db.Model(&Device{}).Where("user_id = ?", userID).Count(&deviceCount).Error; err != nil {
		return fmt.Errorf("查询设备数量失败: %w", err)
	}

	if int(deviceCount) >= user.DeviceLimit {
		return ErrDeviceLimitExceeded
	}

	return nil
}

/**
 * 生成 WireGuard 密钥对
 */
func (s *Service) generateWireGuardKeys() (string, string, error) {
	privateKey, err := wgtypes.GeneratePrivateKey()
	if err != nil {
		return "", "", ErrKeyGenerationFailed
	}

	publicKey := privateKey.PublicKey()

	return privateKey.String(), publicKey.String(), nil
}

/**
 * 分配接口IP地址
 */
func (s *Service) allocateInterfaceIP(userID uint) (string, error) {
	// 简单的IP分配策略：基于用户ID和设备数量
	var deviceCount int64
	if err := s.db.Model(&Device{}).Where("user_id = ?", userID).Count(&deviceCount).Error; err != nil {
		return "", fmt.Errorf("查询设备数量失败: %w", err)
	}

	// 计算IP地址：10.{userID}.{deviceCount+1}.1/24
	subnet := fmt.Sprintf("10.%d.%d.1/24", userID%255, (deviceCount+1)%255)
	
	return subnet, nil
}

/**
 * 获取对等节点列表
 */
func (s *Service) getPeerList(userID, excludeDeviceID uint) ([]PeerInfo, error) {
	var devices []Device
	
	// 查询同一用户的其他设备
	if err := s.db.Where("user_id = ? AND id != ? AND status = ?", userID, excludeDeviceID, DeviceStatusOnline).Find(&devices).Error; err != nil {
		return nil, fmt.Errorf("查询对等设备失败: %w", err)
	}

	peers := make([]PeerInfo, 0, len(devices))
	for _, device := range devices {
		peer := PeerInfo{
			DeviceUUID:    device.DeviceUUID,
			DeviceName:    device.DeviceName,
			PublicKey:     device.WireguardPublicKey,
			InterfaceIP:   device.InterfaceIP,
			Endpoint:      fmt.Sprintf("%s:51820", device.PublicIP), // 默认端口
			AllowedIPs:    device.InterfaceIP,
			LastHandshake: device.LastHeartbeat.Format(time.RFC3339),
		}
		peers = append(peers, peer)
	}

	return peers, nil
}
