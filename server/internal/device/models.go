package device

import (
	"time"
)

/**
 * 设备管理模块 - 数据模型
 * 
 * 功能说明:
 * - 定义设备相关的数据结构
 * - 包含设备信息、心跳请求等模型
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2025-06-14
 */

// 设备状态常量
const (
	DeviceStatusOnline  = "online"  // 设备在线
	DeviceStatusOffline = "offline" // 设备离线
)

// 心跳超时时间
const (
	HeartbeatTimeout = 30 * time.Second // 心跳超时时间
)

/**
 * 设备信息结构体
 */
type Device struct {
	ID                  uint      `json:"id" gorm:"primaryKey"`
	UserID              uint      `json:"user_id" gorm:"not null;index"`
	DeviceUUID          string    `json:"device_uuid" gorm:"uniqueIndex;size:36;not null"`
	DeviceName          string    `json:"device_name" gorm:"size:100;not null"`
	MacAddress          string    `json:"mac_address" gorm:"size:17"`
	LanIP               string    `json:"lan_ip"`
	PublicIP            string    `json:"public_ip"`
	WireguardPrivateKey string    `json:"-" gorm:"type:text"`
	WireguardPublicKey  string    `json:"wireguard_public_key" gorm:"type:text"`
	InterfaceIP         string    `json:"interface_ip"`
	LastHeartbeat       time.Time `json:"last_heartbeat"`
	Status              string    `json:"status" gorm:"size:20;default:offline"`
	CreatedAt           time.Time `json:"created_at"`
	UpdatedAt           time.Time `json:"updated_at"`
}

/**
 * 设备注册请求结构体
 */
type RegisterRequest struct {
	DeviceUUID string `json:"device_uuid" binding:"required,uuid" example:"550e8400-e29b-41d4-a716-************"`
	DeviceName string `json:"device_name" binding:"required,min=1,max=100" example:"laptop-work"`
	MacAddress string `json:"mac_address" binding:"omitempty,mac" example:"00:11:22:33:44:55"`
	LanIP      string `json:"lan_ip" binding:"omitempty,ip" example:"*************"`
	PublicIP   string `json:"public_ip" binding:"omitempty,ip" example:"***********"`
}

/**
 * 设备心跳请求结构体
 */
type HeartbeatRequest struct {
	DeviceUUID string `json:"device_uuid" binding:"required,uuid" example:"550e8400-e29b-41d4-a716-************"`
	DeviceName string `json:"device_name" binding:"required" example:"laptop-work"`
	MacAddress string `json:"mac_address" binding:"omitempty,mac" example:"00:11:22:33:44:55"`
	LanIP      string `json:"lan_ip" binding:"omitempty,ip" example:"*************"`
	PublicIP   string `json:"public_ip" binding:"omitempty,ip" example:"***********"`
	Route1     string `json:"route1" binding:"omitempty" example:"***********/24"`
	Route2     string `json:"route2" binding:"omitempty" example:"10.0.0.0/8"`
}

/**
 * 设备心跳响应结构体
 */
type HeartbeatResponse struct {
	Success       bool        `json:"success" example:"true"`
	Message       string      `json:"message" example:"心跳接收成功"`
	ConfigUpdated bool        `json:"config_updated" example:"false"`
	Peers         []PeerInfo  `json:"peers,omitempty"`
}

/**
 * 对等节点信息结构体
 */
type PeerInfo struct {
	DeviceUUID    string `json:"device_uuid" example:"550e8400-e29b-41d4-a716-************"`
	DeviceName    string `json:"device_name" example:"phone-alice"`
	PublicKey     string `json:"public_key" example:"base64_encoded_public_key"`
	InterfaceIP   string `json:"interface_ip" example:"********/24"`
	Endpoint      string `json:"endpoint" example:"***********:51820"`
	AllowedIPs    string `json:"allowed_ips" example:"********/32"`
	LastHandshake string `json:"last_handshake" example:"2025-06-14T10:00:00Z"`
}

/**
 * 设备注册响应结构体
 */
type RegisterResponse struct {
	Success bool   `json:"success" example:"true"`
	Message string `json:"message" example:"设备注册成功"`
	Device  *Device `json:"device,omitempty"`
}

/**
 * 设备列表响应结构体
 */
type ListResponse struct {
	Success bool      `json:"success" example:"true"`
	Message string    `json:"message" example:"获取设备列表成功"`
	Devices []Device  `json:"devices"`
	Total   int64     `json:"total" example:"5"`
}

/**
 * 设备配置响应结构体
 */
type ConfigResponse struct {
	Success    bool   `json:"success" example:"true"`
	Message    string `json:"message" example:"获取配置成功"`
	ConfigData []byte `json:"config_data,omitempty"` // 加密的配置数据
	ConfigHash string `json:"config_hash" example:"sha256_hash"`
}

/**
 * 设备统计信息结构体
 */
type DeviceStats struct {
	TotalDevices  int64 `json:"total_devices"`
	OnlineDevices int64 `json:"online_devices"`
	OfflineDevices int64 `json:"offline_devices"`
}

/**
 * 设备更新请求结构体
 */
type UpdateRequest struct {
	DeviceName string `json:"device_name" binding:"omitempty,min=1,max=100" example:"laptop-work-updated"`
	MacAddress string `json:"mac_address" binding:"omitempty,mac" example:"00:11:22:33:44:66"`
	LanIP      string `json:"lan_ip" binding:"omitempty,ip" example:"*************"`
	PublicIP   string `json:"public_ip" binding:"omitempty,ip" example:"***********"`
}

/**
 * 通用响应结构体
 */
type Response struct {
	Success bool        `json:"success" example:"true"`
	Message string      `json:"message" example:"操作成功"`
	Data    interface{} `json:"data,omitempty"`
}

/**
 * 验证设备状态是否有效
 * 
 * @param status 设备状态
 * @return bool 是否有效
 */
func IsValidDeviceStatus(status string) bool {
	return status == DeviceStatusOnline || status == DeviceStatusOffline
}

/**
 * 检查设备是否在线
 * 
 * @param lastHeartbeat 最后心跳时间
 * @return bool 是否在线
 */
func IsDeviceOnline(lastHeartbeat time.Time) bool {
	return time.Since(lastHeartbeat) <= HeartbeatTimeout
}

/**
 * 表名映射
 */
func (Device) TableName() string {
	return "devices"
}
