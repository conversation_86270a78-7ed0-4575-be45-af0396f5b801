package database

import (
	"fmt"
	"log"
	"os"
	"time"

	"github.com/go-redis/redis/v8"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

/**
 * 数据库连接管理模块
 * 
 * 功能说明:
 * - 管理 PostgreSQL 数据库连接
 * - 管理 Redis 缓存连接
 * - 提供数据库初始化和关闭功能
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2025-06-14
 */

var (
	// DB PostgreSQL 数据库连接实例
	DB *gorm.DB
	
	// RedisClient Redis 客户端实例
	RedisClient *redis.Client
)

/**
 * 数据库配置结构体
 */
type Config struct {
	Host     string
	Port     string
	User     string
	Password string
	DBName   string
	SSLMode  string
}

/**
 * Redis 配置结构体
 */
type RedisConfig struct {
	Host     string
	Port     string
	Password string
	DB       int
}

/**
 * 初始化数据库连接
 * 
 * @return error 初始化错误信息
 */
func Initialize() error {
	// 初始化 PostgreSQL
	if err := initPostgreSQL(); err != nil {
		return fmt.Errorf("PostgreSQL 初始化失败: %w", err)
	}

	// 初始化 Redis
	if err := initRedis(); err != nil {
		return fmt.Errorf("Redis 初始化失败: %w", err)
	}

	log.Println("数据库连接初始化成功")
	return nil
}

/**
 * 初始化 PostgreSQL 连接
 * 
 * @return error 初始化错误信息
 */
func initPostgreSQL() error {
	config := getPostgreSQLConfig()
	
	// 构建 DSN
	dsn := fmt.Sprintf("host=%s user=%s password=%s dbname=%s port=%s sslmode=%s TimeZone=Asia/Shanghai",
		config.Host, config.User, config.Password, config.DBName, config.Port, config.SSLMode)

	// 配置 GORM 日志
	gormConfig := &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	}

	// 连接数据库
	var err error
	DB, err = gorm.Open(postgres.Open(dsn), gormConfig)
	if err != nil {
		return fmt.Errorf("连接 PostgreSQL 失败: %w", err)
	}

	// 获取底层 sql.DB 对象进行连接池配置
	sqlDB, err := DB.DB()
	if err != nil {
		return fmt.Errorf("获取 SQL DB 实例失败: %w", err)
	}

	// 设置连接池参数
	sqlDB.SetMaxIdleConns(10)                   // 最大空闲连接数
	sqlDB.SetMaxOpenConns(100)                  // 最大打开连接数
	sqlDB.SetConnMaxLifetime(time.Hour)         // 连接最大生存时间

	// 测试连接
	if err := sqlDB.Ping(); err != nil {
		return fmt.Errorf("PostgreSQL 连接测试失败: %w", err)
	}

	log.Println("PostgreSQL 连接成功")
	return nil
}

/**
 * 初始化 Redis 连接
 * 
 * @return error 初始化错误信息
 */
func initRedis() error {
	config := getRedisConfig()
	
	// 创建 Redis 客户端
	RedisClient = redis.NewClient(&redis.Options{
		Addr:     fmt.Sprintf("%s:%s", config.Host, config.Port),
		Password: config.Password,
		DB:       config.DB,
	})

	// 测试连接
	ctx := RedisClient.Context()
	_, err := RedisClient.Ping(ctx).Result()
	if err != nil {
		return fmt.Errorf("Redis 连接测试失败: %w", err)
	}

	log.Println("Redis 连接成功")
	return nil
}

/**
 * 获取 PostgreSQL 配置
 * 
 * @return Config PostgreSQL 配置
 */
func getPostgreSQLConfig() Config {
	return Config{
		Host:     getEnv("DB_HOST", "localhost"),
		Port:     getEnv("DB_PORT", "5432"),
		User:     getEnv("DB_USER", "postgres"),
		Password: getEnv("DB_PASSWORD", "password"),
		DBName:   getEnv("DB_NAME", "meshvpn"),
		SSLMode:  getEnv("DB_SSLMODE", "disable"),
	}
}

/**
 * 获取 Redis 配置
 * 
 * @return RedisConfig Redis 配置
 */
func getRedisConfig() RedisConfig {
	return RedisConfig{
		Host:     getEnv("REDIS_HOST", "localhost"),
		Port:     getEnv("REDIS_PORT", "6379"),
		Password: getEnv("REDIS_PASSWORD", ""),
		DB:       0, // 默认使用 DB 0
	}
}

/**
 * 获取环境变量，如果不存在则返回默认值
 * 
 * @param key 环境变量键名
 * @param defaultValue 默认值
 * @return string 环境变量值或默认值
 */
func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

/**
 * 关闭数据库连接
 */
func Close() {
	// 关闭 PostgreSQL 连接
	if DB != nil {
		sqlDB, err := DB.DB()
		if err == nil {
			sqlDB.Close()
		}
		log.Println("PostgreSQL 连接已关闭")
	}

	// 关闭 Redis 连接
	if RedisClient != nil {
		RedisClient.Close()
		log.Println("Redis 连接已关闭")
	}
}

/**
 * 获取数据库连接实例
 * 
 * @return *gorm.DB 数据库连接实例
 */
func GetDB() *gorm.DB {
	return DB
}

/**
 * 获取 Redis 客户端实例
 * 
 * @return *redis.Client Redis 客户端实例
 */
func GetRedis() *redis.Client {
	return RedisClient
}
