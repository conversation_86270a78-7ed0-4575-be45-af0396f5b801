package group

import (
	"errors"
	"fmt"
	"net"
	"time"

	"gorm.io/gorm"

	"github.com/yytcloud/meshvpn/server/internal/database"
)

/**
 * 分组管理模块 - 服务层
 * 
 * 功能说明:
 * - 处理分组创建、更新、删除等业务逻辑
 * - 管理设备与分组的关联关系
 * - 提供分组信息查询功能
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2025-06-14
 */

// 错误定义
var (
	ErrGroupNotFound      = errors.New("分组不存在")
	ErrGroupExists        = errors.New("分组已存在")
	ErrInvalidSubnet      = errors.New("无效的子网格式")
	ErrDeviceNotFound     = errors.New("设备不存在")
	ErrDeviceAlreadyInGroup = errors.New("设备已在分组中")
	ErrSubnetConflict     = errors.New("子网冲突")
)

/**
 * 分组服务结构体
 */
type Service struct {
	db *gorm.DB
}

/**
 * 创建分组服务实例
 * 
 * @return *Service 分组服务实例
 */
func NewService() *Service {
	return &Service{
		db: database.GetDB(),
	}
}

/**
 * 创建分组
 * 
 * @param userID 用户ID
 * @param req 创建请求
 * @return *Group 创建的分组信息
 * @return error 错误信息
 */
func (s *Service) CreateGroup(userID uint, req *CreateRequest) (*Group, error) {
	// 验证子网格式
	if !s.validateSubnet(req.Subnet) {
		return nil, ErrInvalidSubnet
	}

	// 检查分组名称是否已存在
	var existingGroup Group
	err := s.db.Where("user_id = ? AND group_name = ?", userID, req.GroupName).First(&existingGroup).Error
	if err == nil {
		return nil, ErrGroupExists
	}

	// 检查子网是否冲突
	if s.checkSubnetConflict(userID, req.Subnet) {
		return nil, ErrSubnetConflict
	}

	// 创建分组
	group := &Group{
		UserID:    userID,
		GroupName: req.GroupName,
		Subnet:    req.Subnet,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	if err := s.db.Create(group).Error; err != nil {
		return nil, fmt.Errorf("创建分组失败: %w", err)
	}

	return group, nil
}

/**
 * 获取用户分组列表
 * 
 * @param userID 用户ID
 * @param limit 限制数量
 * @param offset 偏移量
 * @return []Group 分组列表
 * @return int64 总数量
 * @return error 错误信息
 */
func (s *Service) GetUserGroups(userID uint, limit, offset int) ([]Group, int64, error) {
	var groups []Group
	var total int64

	// 获取总数
	if err := s.db.Model(&Group{}).Where("user_id = ?", userID).Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("查询分组总数失败: %w", err)
	}

	// 获取分组列表
	query := s.db.Where("user_id = ?", userID).Order("created_at DESC")
	if limit > 0 {
		query = query.Limit(limit)
	}
	if offset > 0 {
		query = query.Offset(offset)
	}

	if err := query.Find(&groups).Error; err != nil {
		return nil, 0, fmt.Errorf("查询分组列表失败: %w", err)
	}

	return groups, total, nil
}

/**
 * 根据ID获取分组信息
 * 
 * @param groupID 分组ID
 * @param userID 用户ID
 * @return *Group 分组信息
 * @return error 错误信息
 */
func (s *Service) GetGroupByID(groupID, userID uint) (*Group, error) {
	var group Group
	
	err := s.db.Where("id = ? AND user_id = ?", groupID, userID).First(&group).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, ErrGroupNotFound
		}
		return nil, fmt.Errorf("查询分组失败: %w", err)
	}

	return &group, nil
}

/**
 * 获取分组详情（包含设备信息）
 * 
 * @param groupID 分组ID
 * @param userID 用户ID
 * @return *GroupDetail 分组详情
 * @return error 错误信息
 */
func (s *Service) GetGroupDetail(groupID, userID uint) (*GroupDetail, error) {
	// 获取分组基本信息
	group, err := s.GetGroupByID(groupID, userID)
	if err != nil {
		return nil, err
	}

	// 获取分组中的设备
	devices, err := s.getGroupDevices(groupID)
	if err != nil {
		return nil, fmt.Errorf("获取分组设备失败: %w", err)
	}

	detail := &GroupDetail{
		ID:        group.ID,
		UserID:    group.UserID,
		GroupName: group.GroupName,
		Subnet:    group.Subnet,
		CreatedAt: group.CreatedAt,
		UpdatedAt: group.UpdatedAt,
		Devices:   devices,
	}

	return detail, nil
}

/**
 * 更新分组信息
 * 
 * @param groupID 分组ID
 * @param userID 用户ID
 * @param req 更新请求
 * @return error 错误信息
 */
func (s *Service) UpdateGroup(groupID, userID uint, req *UpdateRequest) error {
	// 检查分组是否存在
	_, err := s.GetGroupByID(groupID, userID)
	if err != nil {
		return err
	}

	updates := map[string]interface{}{
		"updated_at": time.Now(),
	}

	// 更新分组名称
	if req.GroupName != "" {
		// 检查新名称是否冲突
		var existingGroup Group
		err := s.db.Where("user_id = ? AND group_name = ? AND id != ?", userID, req.GroupName, groupID).First(&existingGroup).Error
		if err == nil {
			return ErrGroupExists
		}
		updates["group_name"] = req.GroupName
	}

	// 更新子网
	if req.Subnet != "" {
		if !s.validateSubnet(req.Subnet) {
			return ErrInvalidSubnet
		}
		// 检查子网冲突（排除当前分组）
		if s.checkSubnetConflictExclude(userID, req.Subnet, groupID) {
			return ErrSubnetConflict
		}
		updates["subnet"] = req.Subnet
	}

	// 执行更新
	result := s.db.Model(&Group{}).Where("id = ? AND user_id = ?", groupID, userID).Updates(updates)
	if result.Error != nil {
		return fmt.Errorf("更新分组失败: %w", result.Error)
	}

	return nil
}

/**
 * 删除分组
 * 
 * @param groupID 分组ID
 * @param userID 用户ID
 * @return error 错误信息
 */
func (s *Service) DeleteGroup(groupID, userID uint) error {
	// 开始事务
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 删除设备关联
	if err := tx.Where("group_id = ?", groupID).Delete(&DeviceGroup{}).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("删除设备关联失败: %w", err)
	}

	// 删除分组
	result := tx.Where("id = ? AND user_id = ?", groupID, userID).Delete(&Group{})
	if result.Error != nil {
		tx.Rollback()
		return fmt.Errorf("删除分组失败: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		tx.Rollback()
		return ErrGroupNotFound
	}

	return tx.Commit().Error
}

/**
 * 添加设备到分组
 * 
 * @param groupID 分组ID
 * @param userID 用户ID
 * @param deviceUUIDs 设备UUID列表
 * @return error 错误信息
 */
func (s *Service) AddDevicesToGroup(groupID, userID uint, deviceUUIDs []string) error {
	// 检查分组是否存在
	_, err := s.GetGroupByID(groupID, userID)
	if err != nil {
		return err
	}

	// 开始事务
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	for _, deviceUUID := range deviceUUIDs {
		// 查找设备
		var device struct {
			ID     uint `json:"id"`
			UserID uint `json:"user_id"`
		}
		
		err := tx.Table("devices").Select("id, user_id").Where("device_uuid = ?", deviceUUID).First(&device).Error
		if err != nil {
			tx.Rollback()
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return ErrDeviceNotFound
			}
			return fmt.Errorf("查询设备失败: %w", err)
		}

		// 检查设备是否属于当前用户
		if device.UserID != userID {
			tx.Rollback()
			return ErrDeviceNotFound
		}

		// 检查设备是否已在分组中
		var existingRelation DeviceGroup
		err = tx.Where("device_id = ? AND group_id = ?", device.ID, groupID).First(&existingRelation).Error
		if err == nil {
			continue // 设备已在分组中，跳过
		}

		// 添加设备到分组
		deviceGroup := DeviceGroup{
			DeviceID: device.ID,
			GroupID:  groupID,
			JoinedAt: time.Now(),
		}

		if err := tx.Create(&deviceGroup).Error; err != nil {
			tx.Rollback()
			return fmt.Errorf("添加设备到分组失败: %w", err)
		}
	}

	return tx.Commit().Error
}

/**
 * 从分组移除设备
 * 
 * @param groupID 分组ID
 * @param userID 用户ID
 * @param deviceID 设备ID
 * @return error 错误信息
 */
func (s *Service) RemoveDeviceFromGroup(groupID, userID, deviceID uint) error {
	// 检查分组是否存在
	_, err := s.GetGroupByID(groupID, userID)
	if err != nil {
		return err
	}

	// 删除关联
	result := s.db.Where("device_id = ? AND group_id = ?", deviceID, groupID).Delete(&DeviceGroup{})
	if result.Error != nil {
		return fmt.Errorf("移除设备失败: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return ErrDeviceNotFound
	}

	return nil
}

/**
 * 获取分组统计信息
 * 
 * @param userID 用户ID
 * @return []GroupStats 分组统计列表
 * @return error 错误信息
 */
func (s *Service) GetGroupStats(userID uint) ([]GroupStats, error) {
	var stats []GroupStats

	query := `
		SELECT 
			g.id as group_id,
			g.group_name,
			COALESCE(COUNT(dg.device_id), 0) as total_devices,
			COALESCE(COUNT(CASE WHEN d.status = 'online' THEN 1 END), 0) as online_devices,
			COALESCE(COUNT(CASE WHEN d.status = 'offline' THEN 1 END), 0) as offline_devices
		FROM groups g
		LEFT JOIN device_groups dg ON g.id = dg.group_id
		LEFT JOIN devices d ON dg.device_id = d.id
		WHERE g.user_id = ?
		GROUP BY g.id, g.group_name
		ORDER BY g.created_at DESC
	`

	if err := s.db.Raw(query, userID).Scan(&stats).Error; err != nil {
		return nil, fmt.Errorf("查询分组统计失败: %w", err)
	}

	return stats, nil
}

/**
 * 验证子网格式
 */
func (s *Service) validateSubnet(subnet string) bool {
	_, _, err := net.ParseCIDR(subnet)
	return err == nil
}

/**
 * 检查子网冲突
 */
func (s *Service) checkSubnetConflict(userID uint, subnet string) bool {
	var count int64
	s.db.Model(&Group{}).Where("user_id = ? AND subnet = ?", userID, subnet).Count(&count)
	return count > 0
}

/**
 * 检查子网冲突（排除指定分组）
 */
func (s *Service) checkSubnetConflictExclude(userID uint, subnet string, excludeGroupID uint) bool {
	var count int64
	s.db.Model(&Group{}).Where("user_id = ? AND subnet = ? AND id != ?", userID, subnet, excludeGroupID).Count(&count)
	return count > 0
}

/**
 * 获取分组中的设备
 */
func (s *Service) getGroupDevices(groupID uint) ([]DeviceInfo, error) {
	var devices []DeviceInfo

	query := `
		SELECT 
			d.id,
			d.device_uuid,
			d.device_name,
			d.interface_ip,
			d.status,
			dg.joined_at
		FROM devices d
		INNER JOIN device_groups dg ON d.id = dg.device_id
		WHERE dg.group_id = ?
		ORDER BY dg.joined_at DESC
	`

	if err := s.db.Raw(query, groupID).Scan(&devices).Error; err != nil {
		return nil, fmt.Errorf("查询分组设备失败: %w", err)
	}

	return devices, nil
}
