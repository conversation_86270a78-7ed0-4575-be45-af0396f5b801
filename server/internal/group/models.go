package group

import (
	"time"
)

/**
 * 分组管理模块 - 数据模型
 * 
 * 功能说明:
 * - 定义分组相关的数据结构
 * - 包含分组信息、设备关联等模型
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2025-06-14
 */

/**
 * 分组信息结构体
 */
type Group struct {
	ID        uint      `json:"id" gorm:"primaryKey"`
	UserID    uint      `json:"user_id" gorm:"not null;index"`
	GroupName string    `json:"group_name" gorm:"size:100;not null"`
	Subnet    string    `json:"subnet" gorm:"not null"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

/**
 * 设备分组关联结构体
 */
type DeviceGroup struct {
	DeviceID uint      `json:"device_id" gorm:"not null"`
	GroupID  uint      `json:"group_id" gorm:"not null"`
	JoinedAt time.Time `json:"joined_at"`
}

/**
 * 分组创建请求结构体
 */
type CreateRequest struct {
	GroupName string `json:"group_name" binding:"required,min=1,max=100" example:"home-network"`
	Subnet    string `json:"subnet" binding:"required,cidr" example:"10.0.1.0/24"`
}

/**
 * 分组更新请求结构体
 */
type UpdateRequest struct {
	GroupName string `json:"group_name" binding:"omitempty,min=1,max=100" example:"home-network-updated"`
	Subnet    string `json:"subnet" binding:"omitempty,cidr" example:"10.0.2.0/24"`
}

/**
 * 分组响应结构体
 */
type GroupResponse struct {
	Success bool   `json:"success" example:"true"`
	Message string `json:"message" example:"操作成功"`
	Group   *Group `json:"group,omitempty"`
}

/**
 * 分组列表响应结构体
 */
type ListResponse struct {
	Success bool    `json:"success" example:"true"`
	Message string  `json:"message" example:"获取分组列表成功"`
	Groups  []Group `json:"groups"`
	Total   int64   `json:"total" example:"3"`
}

/**
 * 分组详情结构体（包含设备信息）
 */
type GroupDetail struct {
	ID        uint           `json:"id"`
	UserID    uint           `json:"user_id"`
	GroupName string         `json:"group_name"`
	Subnet    string         `json:"subnet"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	Devices   []DeviceInfo   `json:"devices"`
}

/**
 * 设备信息结构体（用于分组详情）
 */
type DeviceInfo struct {
	ID          uint      `json:"id"`
	DeviceUUID  string    `json:"device_uuid"`
	DeviceName  string    `json:"device_name"`
	InterfaceIP string    `json:"interface_ip"`
	Status      string    `json:"status"`
	JoinedAt    time.Time `json:"joined_at"`
}

/**
 * 添加设备到分组请求结构体
 */
type AddDeviceRequest struct {
	DeviceUUIDs []string `json:"device_uuids" binding:"required,min=1" example:"[\"550e8400-e29b-41d4-a716-************\"]"`
}

/**
 * 分组统计信息结构体
 */
type GroupStats struct {
	GroupID       uint `json:"group_id"`
	GroupName     string `json:"group_name"`
	TotalDevices  int64 `json:"total_devices"`
	OnlineDevices int64 `json:"online_devices"`
	OfflineDevices int64 `json:"offline_devices"`
}

/**
 * 通用响应结构体
 */
type Response struct {
	Success bool        `json:"success" example:"true"`
	Message string      `json:"message" example:"操作成功"`
	Data    interface{} `json:"data,omitempty"`
}

/**
 * 验证子网格式
 * 
 * @param subnet 子网字符串
 * @return bool 是否有效
 */
func ValidateSubnet(subnet string) bool {
	// 简单的 CIDR 格式验证
	// 实际应该使用 net.ParseCIDR 进行验证
	return len(subnet) > 0 && (subnet[len(subnet)-3:] == "/24" || 
		subnet[len(subnet)-3:] == "/16" || subnet[len(subnet)-3:] == "/8")
}

/**
 * 检查分组名称是否有效
 * 
 * @param groupName 分组名称
 * @return bool 是否有效
 */
func ValidateGroupName(groupName string) bool {
	return len(groupName) > 0 && len(groupName) <= 100
}

/**
 * 表名映射
 */
func (Group) TableName() string {
	return "groups"
}

/**
 * 表名映射
 */
func (DeviceGroup) TableName() string {
	return "device_groups"
}
