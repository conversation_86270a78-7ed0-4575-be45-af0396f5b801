package wireguard

import (
	"archive/tar"
	"bytes"
	"compress/gzip"
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"crypto/sha256"
	"encoding/base64"
	"fmt"
	"io"
	"strings"
	"text/template"
	"time"

	"golang.zx2c4.com/wireguard/wgctrl/wgtypes"
)

/**
 * WireGuard 配置生成器模块
 * 
 * 功能说明:
 * - 生成 WireGuard 配置文件
 * - 加密配置包
 * - 管理密钥对
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2025-06-14
 */

// WireGuard 配置模板
const wgConfigTemplate = `[Interface]
PrivateKey = {{.PrivateKey}}
Address = {{.Address}}
DNS = {{.DNS}}
{{if .MTU}}MTU = {{.MTU}}{{end}}

{{range .Peers}}
[Peer]
PublicKey = {{.PublicKey}}
AllowedIPs = {{.AllowedIPs}}
{{if .Endpoint}}Endpoint = {{.Endpoint}}{{end}}
{{if .PersistentKeepalive}}PersistentKeepalive = {{.PersistentKeepalive}}{{end}}

{{end}}`

/**
 * WireGuard 接口配置结构体
 */
type InterfaceConfig struct {
	PrivateKey string `json:"private_key"`
	Address    string `json:"address"`
	DNS        string `json:"dns"`
	MTU        int    `json:"mtu,omitempty"`
	Peers      []Peer `json:"peers"`
}

/**
 * WireGuard 对等节点结构体
 */
type Peer struct {
	PublicKey           string `json:"public_key"`
	AllowedIPs          string `json:"allowed_ips"`
	Endpoint            string `json:"endpoint,omitempty"`
	PersistentKeepalive int    `json:"persistent_keepalive,omitempty"`
}

/**
 * 设备配置信息结构体
 */
type DeviceConfig struct {
	DeviceUUID         string `json:"device_uuid"`
	DeviceName         string `json:"device_name"`
	PrivateKey         string `json:"private_key"`
	PublicKey          string `json:"public_key"`
	InterfaceIP        string `json:"interface_ip"`
	ServerPublicKey    string `json:"server_public_key"`
	ServerEndpoint     string `json:"server_endpoint"`
	DNS                string `json:"dns"`
	AllowedIPs         string `json:"allowed_ips"`
	PersistentKeepalive int   `json:"persistent_keepalive"`
}

/**
 * 配置生成器结构体
 */
type ConfigGenerator struct {
	serverPrivateKey wgtypes.Key
	serverPublicKey  wgtypes.Key
	serverEndpoint   string
	dnsServers       []string
}

/**
 * 创建配置生成器
 * 
 * @param serverPrivateKey 服务器私钥
 * @param serverEndpoint 服务器端点
 * @param dnsServers DNS 服务器列表
 * @return *ConfigGenerator 配置生成器实例
 * @return error 错误信息
 */
func NewConfigGenerator(serverPrivateKey, serverEndpoint string, dnsServers []string) (*ConfigGenerator, error) {
	// 解析服务器私钥
	privKey, err := wgtypes.ParseKey(serverPrivateKey)
	if err != nil {
		return nil, fmt.Errorf("解析服务器私钥失败: %w", err)
	}

	pubKey := privKey.PublicKey()

	return &ConfigGenerator{
		serverPrivateKey: privKey,
		serverPublicKey:  pubKey,
		serverEndpoint:   serverEndpoint,
		dnsServers:       dnsServers,
	}, nil
}

/**
 * 生成设备配置
 * 
 * @param deviceConfig 设备配置信息
 * @param peers 对等节点列表
 * @return []byte 配置文件内容
 * @return error 错误信息
 */
func (g *ConfigGenerator) GenerateDeviceConfig(deviceConfig *DeviceConfig, peers []Peer) ([]byte, error) {
	// 构建接口配置
	interfaceConfig := InterfaceConfig{
		PrivateKey: deviceConfig.PrivateKey,
		Address:    deviceConfig.InterfaceIP,
		DNS:        strings.Join(g.dnsServers, ", "),
		MTU:        1420, // 默认 MTU
		Peers:      peers,
	}

	// 添加服务器作为对等节点
	serverPeer := Peer{
		PublicKey:           g.serverPublicKey.String(),
		AllowedIPs:          deviceConfig.AllowedIPs,
		Endpoint:            g.serverEndpoint,
		PersistentKeepalive: deviceConfig.PersistentKeepalive,
	}
	interfaceConfig.Peers = append([]Peer{serverPeer}, interfaceConfig.Peers...)

	// 解析模板
	tmpl, err := template.New("wg-config").Parse(wgConfigTemplate)
	if err != nil {
		return nil, fmt.Errorf("解析配置模板失败: %w", err)
	}

	// 生成配置
	var buf bytes.Buffer
	err = tmpl.Execute(&buf, interfaceConfig)
	if err != nil {
		return nil, fmt.Errorf("生成配置失败: %w", err)
	}

	return buf.Bytes(), nil
}

/**
 * 生成加密配置包
 * 
 * @param deviceConfig 设备配置信息
 * @param peers 对等节点列表
 * @param encryptionKey 加密密钥
 * @return []byte 加密的配置包
 * @return string 配置哈希
 * @return error 错误信息
 */
func (g *ConfigGenerator) GenerateEncryptedConfigPackage(deviceConfig *DeviceConfig, peers []Peer, encryptionKey string) ([]byte, string, error) {
	// 生成配置文件
	configData, err := g.GenerateDeviceConfig(deviceConfig, peers)
	if err != nil {
		return nil, "", fmt.Errorf("生成配置失败: %w", err)
	}

	// 创建 tar.gz 包
	tarData, err := g.createTarGzPackage(deviceConfig.DeviceName, configData)
	if err != nil {
		return nil, "", fmt.Errorf("创建配置包失败: %w", err)
	}

	// 计算配置哈希
	hash := sha256.Sum256(tarData)
	configHash := base64.StdEncoding.EncodeToString(hash[:])

	// 加密配置包
	encryptedData, err := g.encryptData(tarData, encryptionKey)
	if err != nil {
		return nil, "", fmt.Errorf("加密配置包失败: %w", err)
	}

	return encryptedData, configHash, nil
}

/**
 * 创建 tar.gz 配置包
 * 
 * @param deviceName 设备名称
 * @param configData 配置数据
 * @return []byte tar.gz 数据
 * @return error 错误信息
 */
func (g *ConfigGenerator) createTarGzPackage(deviceName string, configData []byte) ([]byte, error) {
	var buf bytes.Buffer
	
	// 创建 gzip writer
	gzWriter := gzip.NewWriter(&buf)
	defer gzWriter.Close()

	// 创建 tar writer
	tarWriter := tar.NewWriter(gzWriter)
	defer tarWriter.Close()

	// 添加配置文件到 tar
	configFileName := fmt.Sprintf("%s.conf", deviceName)
	header := &tar.Header{
		Name:    configFileName,
		Mode:    0644,
		Size:    int64(len(configData)),
		ModTime: time.Now(),
	}

	err := tarWriter.WriteHeader(header)
	if err != nil {
		return nil, fmt.Errorf("写入 tar 头失败: %w", err)
	}

	_, err = tarWriter.Write(configData)
	if err != nil {
		return nil, fmt.Errorf("写入配置数据失败: %w", err)
	}

	// 添加元数据文件
	metadata := fmt.Sprintf(`{
  "device_name": "%s",
  "generated_at": "%s",
  "version": "1.0.0"
}`, deviceName, time.Now().Format(time.RFC3339))

	metadataHeader := &tar.Header{
		Name:    "metadata.json",
		Mode:    0644,
		Size:    int64(len(metadata)),
		ModTime: time.Now(),
	}

	err = tarWriter.WriteHeader(metadataHeader)
	if err != nil {
		return nil, fmt.Errorf("写入元数据头失败: %w", err)
	}

	_, err = tarWriter.Write([]byte(metadata))
	if err != nil {
		return nil, fmt.Errorf("写入元数据失败: %w", err)
	}

	return buf.Bytes(), nil
}

/**
 * 加密数据
 * 
 * @param data 原始数据
 * @param key 加密密钥
 * @return []byte 加密后的数据
 * @return error 错误信息
 */
func (g *ConfigGenerator) encryptData(data []byte, key string) ([]byte, error) {
	// 生成 AES 密钥
	keyHash := sha256.Sum256([]byte(key))
	
	// 创建 AES cipher
	block, err := aes.NewCipher(keyHash[:])
	if err != nil {
		return nil, fmt.Errorf("创建 AES cipher 失败: %w", err)
	}

	// 创建 GCM
	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return nil, fmt.Errorf("创建 GCM 失败: %w", err)
	}

	// 生成随机 nonce
	nonce := make([]byte, gcm.NonceSize())
	if _, err := io.ReadFull(rand.Reader, nonce); err != nil {
		return nil, fmt.Errorf("生成 nonce 失败: %w", err)
	}

	// 加密数据
	ciphertext := gcm.Seal(nonce, nonce, data, nil)
	
	return ciphertext, nil
}

/**
 * 解密数据
 * 
 * @param encryptedData 加密的数据
 * @param key 解密密钥
 * @return []byte 解密后的数据
 * @return error 错误信息
 */
func (g *ConfigGenerator) DecryptData(encryptedData []byte, key string) ([]byte, error) {
	// 生成 AES 密钥
	keyHash := sha256.Sum256([]byte(key))
	
	// 创建 AES cipher
	block, err := aes.NewCipher(keyHash[:])
	if err != nil {
		return nil, fmt.Errorf("创建 AES cipher 失败: %w", err)
	}

	// 创建 GCM
	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return nil, fmt.Errorf("创建 GCM 失败: %w", err)
	}

	// 检查数据长度
	nonceSize := gcm.NonceSize()
	if len(encryptedData) < nonceSize {
		return nil, fmt.Errorf("加密数据长度不足")
	}

	// 提取 nonce 和密文
	nonce, ciphertext := encryptedData[:nonceSize], encryptedData[nonceSize:]

	// 解密数据
	plaintext, err := gcm.Open(nil, nonce, ciphertext, nil)
	if err != nil {
		return nil, fmt.Errorf("解密失败: %w", err)
	}

	return plaintext, nil
}

/**
 * 生成 WireGuard 密钥对
 * 
 * @return string 私钥
 * @return string 公钥
 * @return error 错误信息
 */
func GenerateKeyPair() (string, string, error) {
	privateKey, err := wgtypes.GeneratePrivateKey()
	if err != nil {
		return "", "", fmt.Errorf("生成私钥失败: %w", err)
	}

	publicKey := privateKey.PublicKey()

	return privateKey.String(), publicKey.String(), nil
}

/**
 * 验证 WireGuard 密钥格式
 * 
 * @param key 密钥字符串
 * @return bool 是否有效
 */
func ValidateKey(key string) bool {
	_, err := wgtypes.ParseKey(key)
	return err == nil
}
