package user

import (
	"time"
)

/**
 * 用户管理模块 - 数据模型
 * 
 * 功能说明:
 * - 定义用户相关的数据结构
 * - 包含用户信息、订阅计划等模型
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2025-06-14
 */

// 订阅计划常量
const (
	PlanFree      = "free"      // 免费版：5设备
	PlanPro50     = "pro50"     // Pro50：50设备/年 ¥99
	PlanPro100    = "pro100"    // Pro100：100设备/年 ¥188
	PlanUnlimited = "unlimited" // 无限版：无限设备/年 ¥388
)

// 设备限制常量
const (
	DeviceLimitFree      = 5   // 免费版设备限制
	DeviceLimitPro50     = 50  // Pro50设备限制
	DeviceLimitPro100    = 100 // Pro100设备限制
	DeviceLimitUnlimited = -1  // 无限版设备限制 (-1表示无限制)
)

/**
 * 用户信息结构体
 */
type User struct {
	ID               uint      `json:"id" gorm:"primaryKey"`
	Username         string    `json:"username" gorm:"uniqueIndex;size:50;not null"`
	Email            string    `json:"email" gorm:"uniqueIndex;size:100;not null"`
	Phone            string    `json:"phone" gorm:"size:20"`
	PasswordHash     string    `json:"-" gorm:"size:255;not null"`
	SubscriptionPlan string    `json:"subscription_plan" gorm:"size:20;default:free"`
	DeviceLimit      int       `json:"device_limit" gorm:"default:5"`
	CreatedAt        time.Time `json:"created_at"`
	UpdatedAt        time.Time `json:"updated_at"`
}

/**
 * 用户注册请求结构体
 */
type RegisterRequest struct {
	Username string `json:"username" binding:"required,min=3,max=50" example:"alice"`
	Password string `json:"password" binding:"required,min=6,max=100" example:"password123"`
	Email    string `json:"email" binding:"required,email,max=100" example:"<EMAIL>"`
	Phone    string `json:"phone" binding:"omitempty,max=20" example:"+86138****1234"`
}

/**
 * 用户登录请求结构体
 */
type LoginRequest struct {
	Username string `json:"username" binding:"required" example:"alice"`
	Password string `json:"password" binding:"required" example:"password123"`
}

/**
 * 用户登录响应结构体
 */
type LoginResponse struct {
	Success   bool   `json:"success" example:"true"`
	Message   string `json:"message" example:"登录成功"`
	Token     string `json:"token" example:"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."`
	ExpiresIn int64  `json:"expires_in" example:"86400"`
	User      *User  `json:"user"`
}

/**
 * 用户注册响应结构体
 */
type RegisterResponse struct {
	Success bool   `json:"success" example:"true"`
	Message string `json:"message" example:"注册成功"`
	UserID  uint   `json:"user_id" example:"1"`
}

/**
 * 通用响应结构体
 */
type Response struct {
	Success bool        `json:"success" example:"true"`
	Message string      `json:"message" example:"操作成功"`
	Data    interface{} `json:"data,omitempty"`
}

/**
 * 用户统计信息结构体
 */
type UserStats struct {
	UserID              uint   `json:"user_id"`
	Username            string `json:"username"`
	SubscriptionPlan    string `json:"subscription_plan"`
	DeviceLimit         int    `json:"device_limit"`
	CurrentDeviceCount  int    `json:"current_device_count"`
	OnlineDeviceCount   int    `json:"online_device_count"`
}

/**
 * 订阅计划信息结构体
 */
type SubscriptionPlan struct {
	Plan        string `json:"plan"`
	Name        string `json:"name"`
	DeviceLimit int    `json:"device_limit"`
	Price       int    `json:"price"` // 价格（分）
	Duration    string `json:"duration"`
}

/**
 * 获取所有订阅计划信息
 * 
 * @return []SubscriptionPlan 订阅计划列表
 */
func GetSubscriptionPlans() []SubscriptionPlan {
	return []SubscriptionPlan{
		{
			Plan:        PlanFree,
			Name:        "免费版",
			DeviceLimit: DeviceLimitFree,
			Price:       0,
			Duration:    "永久",
		},
		{
			Plan:        PlanPro50,
			Name:        "Pro50",
			DeviceLimit: DeviceLimitPro50,
			Price:       9900, // ¥99.00
			Duration:    "1年",
		},
		{
			Plan:        PlanPro100,
			Name:        "Pro100",
			DeviceLimit: DeviceLimitPro100,
			Price:       18800, // ¥188.00
			Duration:    "1年",
		},
		{
			Plan:        PlanUnlimited,
			Name:        "无限版",
			DeviceLimit: DeviceLimitUnlimited,
			Price:       38800, // ¥388.00
			Duration:    "1年",
		},
	}
}

/**
 * 根据订阅计划获取设备限制
 * 
 * @param plan 订阅计划
 * @return int 设备限制数量
 */
func GetDeviceLimitByPlan(plan string) int {
	switch plan {
	case PlanFree:
		return DeviceLimitFree
	case PlanPro50:
		return DeviceLimitPro50
	case PlanPro100:
		return DeviceLimitPro100
	case PlanUnlimited:
		return DeviceLimitUnlimited
	default:
		return DeviceLimitFree
	}
}

/**
 * 验证订阅计划是否有效
 * 
 * @param plan 订阅计划
 * @return bool 是否有效
 */
func IsValidSubscriptionPlan(plan string) bool {
	validPlans := []string{PlanFree, PlanPro50, PlanPro100, PlanUnlimited}
	for _, validPlan := range validPlans {
		if plan == validPlan {
			return true
		}
	}
	return false
}

/**
 * 表名映射
 */
func (User) TableName() string {
	return "users"
}
