package user

import (
	"errors"
	"fmt"
	"time"

	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"

	"github.com/yytcloud/meshvpn/server/internal/database"
)

/**
 * 用户管理模块 - 服务层
 * 
 * 功能说明:
 * - 处理用户注册、登录、认证等业务逻辑
 * - 管理用户订阅计划和设备限制
 * - 提供用户信息查询和更新功能
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2025-06-14
 */

// 错误定义
var (
	ErrUserNotFound      = errors.New("用户不存在")
	ErrUserExists        = errors.New("用户已存在")
	ErrEmailExists       = errors.New("邮箱已被使用")
	ErrInvalidPassword   = errors.New("密码错误")
	ErrInvalidPlan       = errors.New("无效的订阅计划")
	ErrDeviceLimitExceeded = errors.New("设备数量超过限制")
)

/**
 * 用户服务结构体
 */
type Service struct {
	db *gorm.DB
}

/**
 * 创建用户服务实例
 * 
 * @return *Service 用户服务实例
 */
func NewService() *Service {
	return &Service{
		db: database.GetDB(),
	}
}

/**
 * 用户注册
 * 
 * @param req 注册请求
 * @return *User 创建的用户信息
 * @return error 错误信息
 */
func (s *Service) Register(req *RegisterRequest) (*User, error) {
	// 检查用户名是否已存在
	var existingUser User
	if err := s.db.Where("username = ?", req.Username).First(&existingUser).Error; err == nil {
		return nil, ErrUserExists
	}

	// 检查邮箱是否已存在
	if err := s.db.Where("email = ?", req.Email).First(&existingUser).Error; err == nil {
		return nil, ErrEmailExists
	}

	// 加密密码
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.Password), bcrypt.DefaultCost)
	if err != nil {
		return nil, fmt.Errorf("密码加密失败: %w", err)
	}

	// 创建用户
	user := &User{
		Username:         req.Username,
		Email:            req.Email,
		Phone:            req.Phone,
		PasswordHash:     string(hashedPassword),
		SubscriptionPlan: PlanFree,
		DeviceLimit:      DeviceLimitFree,
		CreatedAt:        time.Now(),
		UpdatedAt:        time.Now(),
	}

	if err := s.db.Create(user).Error; err != nil {
		return nil, fmt.Errorf("创建用户失败: %w", err)
	}

	return user, nil
}

/**
 * 用户登录认证
 * 
 * @param req 登录请求
 * @return *User 用户信息
 * @return error 错误信息
 */
func (s *Service) Login(req *LoginRequest) (*User, error) {
	var user User
	
	// 查找用户
	if err := s.db.Where("username = ?", req.Username).First(&user).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, ErrUserNotFound
		}
		return nil, fmt.Errorf("查询用户失败: %w", err)
	}

	// 验证密码
	if err := bcrypt.CompareHashAndPassword([]byte(user.PasswordHash), []byte(req.Password)); err != nil {
		return nil, ErrInvalidPassword
	}

	return &user, nil
}

/**
 * 根据用户ID获取用户信息
 * 
 * @param userID 用户ID
 * @return *User 用户信息
 * @return error 错误信息
 */
func (s *Service) GetUserByID(userID uint) (*User, error) {
	var user User
	
	if err := s.db.First(&user, userID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, ErrUserNotFound
		}
		return nil, fmt.Errorf("查询用户失败: %w", err)
	}

	return &user, nil
}

/**
 * 根据用户名获取用户信息
 * 
 * @param username 用户名
 * @return *User 用户信息
 * @return error 错误信息
 */
func (s *Service) GetUserByUsername(username string) (*User, error) {
	var user User
	
	if err := s.db.Where("username = ?", username).First(&user).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, ErrUserNotFound
		}
		return nil, fmt.Errorf("查询用户失败: %w", err)
	}

	return &user, nil
}

/**
 * 更新用户订阅计划
 * 
 * @param userID 用户ID
 * @param plan 新的订阅计划
 * @return error 错误信息
 */
func (s *Service) UpdateSubscriptionPlan(userID uint, plan string) error {
	// 验证订阅计划
	if !IsValidSubscriptionPlan(plan) {
		return ErrInvalidPlan
	}

	// 获取设备限制
	deviceLimit := GetDeviceLimitByPlan(plan)

	// 更新用户信息
	result := s.db.Model(&User{}).Where("id = ?", userID).Updates(map[string]interface{}{
		"subscription_plan": plan,
		"device_limit":      deviceLimit,
		"updated_at":        time.Now(),
	})

	if result.Error != nil {
		return fmt.Errorf("更新订阅计划失败: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return ErrUserNotFound
	}

	return nil
}

/**
 * 获取用户统计信息
 * 
 * @param userID 用户ID
 * @return *UserStats 用户统计信息
 * @return error 错误信息
 */
func (s *Service) GetUserStats(userID uint) (*UserStats, error) {
	var stats UserStats
	
	query := `
		SELECT 
			u.id as user_id,
			u.username,
			u.subscription_plan,
			u.device_limit,
			COALESCE(COUNT(d.id), 0) as current_device_count,
			COALESCE(COUNT(CASE WHEN d.status = 'online' THEN 1 END), 0) as online_device_count
		FROM users u
		LEFT JOIN devices d ON u.id = d.user_id
		WHERE u.id = ?
		GROUP BY u.id, u.username, u.subscription_plan, u.device_limit
	`

	if err := s.db.Raw(query, userID).Scan(&stats).Error; err != nil {
		return nil, fmt.Errorf("查询用户统计信息失败: %w", err)
	}

	// 如果没有找到用户
	if stats.UserID == 0 {
		return nil, ErrUserNotFound
	}

	return &stats, nil
}

/**
 * 检查用户是否可以添加新设备
 * 
 * @param userID 用户ID
 * @return bool 是否可以添加
 * @return error 错误信息
 */
func (s *Service) CanAddDevice(userID uint) (bool, error) {
	stats, err := s.GetUserStats(userID)
	if err != nil {
		return false, err
	}

	// -1 表示无限制
	if stats.DeviceLimit == -1 {
		return true, nil
	}

	return stats.CurrentDeviceCount < stats.DeviceLimit, nil
}

/**
 * 更新用户密码
 * 
 * @param userID 用户ID
 * @param oldPassword 旧密码
 * @param newPassword 新密码
 * @return error 错误信息
 */
func (s *Service) UpdatePassword(userID uint, oldPassword, newPassword string) error {
	// 获取用户信息
	user, err := s.GetUserByID(userID)
	if err != nil {
		return err
	}

	// 验证旧密码
	if err := bcrypt.CompareHashAndPassword([]byte(user.PasswordHash), []byte(oldPassword)); err != nil {
		return ErrInvalidPassword
	}

	// 加密新密码
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(newPassword), bcrypt.DefaultCost)
	if err != nil {
		return fmt.Errorf("密码加密失败: %w", err)
	}

	// 更新密码
	result := s.db.Model(&User{}).Where("id = ?", userID).Updates(map[string]interface{}{
		"password_hash": string(hashedPassword),
		"updated_at":    time.Now(),
	})

	if result.Error != nil {
		return fmt.Errorf("更新密码失败: %w", result.Error)
	}

	return nil
}

/**
 * 删除用户
 * 
 * @param userID 用户ID
 * @return error 错误信息
 */
func (s *Service) DeleteUser(userID uint) error {
	result := s.db.Delete(&User{}, userID)
	
	if result.Error != nil {
		return fmt.Errorf("删除用户失败: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return ErrUserNotFound
	}

	return nil
}
