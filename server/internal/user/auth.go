package user

import (
	"errors"
	"os"
	"strconv"
	"time"

	"github.com/golang-jwt/jwt/v5"
)

/**
 * JWT 认证模块
 * 
 * 功能说明:
 * - 生成和验证 JWT Token
 * - 管理用户认证状态
 * - 提供中间件认证功能
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2025-06-14
 */

// JWT 相关常量
const (
	DefaultTokenExpiration = 24 * time.Hour // 默认 Token 过期时间
	DefaultJWTSecret       = "meshvpn_default_secret_key_change_in_production"
)

// 错误定义
var (
	ErrInvalidToken = errors.New("无效的 Token")
	ErrExpiredToken = errors.New("Token 已过期")
	ErrTokenClaims  = errors.New("Token 声明解析失败")
)

/**
 * JWT 声明结构体
 */
type Claims struct {
	UserID   uint   `json:"user_id"`
	Username string `json:"username"`
	jwt.RegisteredClaims
}

/**
 * JWT 管理器结构体
 */
type JWTManager struct {
	secretKey []byte
	expiration time.Duration
}

/**
 * 创建 JWT 管理器实例
 * 
 * @return *JWTManager JWT 管理器实例
 */
func NewJWTManager() *JWTManager {
	secret := os.Getenv("JWT_SECRET")
	if secret == "" {
		secret = DefaultJWTSecret
	}

	expiration := DefaultTokenExpiration
	if expStr := os.Getenv("JWT_EXPIRATION_HOURS"); expStr != "" {
		if hours, err := strconv.Atoi(expStr); err == nil {
			expiration = time.Duration(hours) * time.Hour
		}
	}

	return &JWTManager{
		secretKey:  []byte(secret),
		expiration: expiration,
	}
}

/**
 * 生成 JWT Token
 * 
 * @param user 用户信息
 * @return string JWT Token
 * @return error 错误信息
 */
func (j *JWTManager) GenerateToken(user *User) (string, error) {
	now := time.Now()
	
	claims := &Claims{
		UserID:   user.ID,
		Username: user.Username,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(now.Add(j.expiration)),
			IssuedAt:  jwt.NewNumericDate(now),
			NotBefore: jwt.NewNumericDate(now),
			Issuer:    "meshvpn",
			Subject:   strconv.Itoa(int(user.ID)),
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	tokenString, err := token.SignedString(j.secretKey)
	if err != nil {
		return "", err
	}

	return tokenString, nil
}

/**
 * 验证 JWT Token
 * 
 * @param tokenString JWT Token 字符串
 * @return *Claims Token 声明
 * @return error 错误信息
 */
func (j *JWTManager) ValidateToken(tokenString string) (*Claims, error) {
	token, err := jwt.ParseWithClaims(tokenString, &Claims{}, func(token *jwt.Token) (interface{}, error) {
		// 验证签名方法
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, errors.New("无效的签名方法")
		}
		return j.secretKey, nil
	})

	if err != nil {
		if errors.Is(err, jwt.ErrTokenExpired) {
			return nil, ErrExpiredToken
		}
		return nil, ErrInvalidToken
	}

	claims, ok := token.Claims.(*Claims)
	if !ok || !token.Valid {
		return nil, ErrTokenClaims
	}

	return claims, nil
}

/**
 * 刷新 JWT Token
 * 
 * @param tokenString 原 JWT Token
 * @return string 新的 JWT Token
 * @return error 错误信息
 */
func (j *JWTManager) RefreshToken(tokenString string) (string, error) {
	claims, err := j.ValidateToken(tokenString)
	if err != nil {
		return "", err
	}

	// 检查 Token 是否在刷新窗口内（过期前 1 小时内可以刷新）
	refreshWindow := time.Hour
	if time.Until(claims.ExpiresAt.Time) > refreshWindow {
		return "", errors.New("Token 还未到刷新时间")
	}

	// 创建新的声明
	now := time.Now()
	newClaims := &Claims{
		UserID:   claims.UserID,
		Username: claims.Username,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(now.Add(j.expiration)),
			IssuedAt:  jwt.NewNumericDate(now),
			NotBefore: jwt.NewNumericDate(now),
			Issuer:    "meshvpn",
			Subject:   claims.Subject,
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, newClaims)
	return token.SignedString(j.secretKey)
}

/**
 * 获取 Token 过期时间（秒）
 * 
 * @return int64 过期时间秒数
 */
func (j *JWTManager) GetExpirationSeconds() int64 {
	return int64(j.expiration.Seconds())
}

/**
 * 从 Token 字符串中提取用户ID（不验证 Token 有效性）
 * 
 * @param tokenString JWT Token 字符串
 * @return uint 用户ID
 * @return error 错误信息
 */
func (j *JWTManager) ExtractUserID(tokenString string) (uint, error) {
	token, _, err := new(jwt.Parser).ParseUnverified(tokenString, &Claims{})
	if err != nil {
		return 0, err
	}

	claims, ok := token.Claims.(*Claims)
	if !ok {
		return 0, ErrTokenClaims
	}

	return claims.UserID, nil
}

/**
 * 检查 Token 是否即将过期
 * 
 * @param tokenString JWT Token 字符串
 * @param threshold 阈值时间
 * @return bool 是否即将过期
 * @return error 错误信息
 */
func (j *JWTManager) IsTokenExpiringSoon(tokenString string, threshold time.Duration) (bool, error) {
	claims, err := j.ValidateToken(tokenString)
	if err != nil {
		return false, err
	}

	return time.Until(claims.ExpiresAt.Time) < threshold, nil
}

/**
 * 全局 JWT 管理器实例
 */
var GlobalJWTManager = NewJWTManager()

/**
 * 便捷函数：生成 Token
 */
func GenerateToken(user *User) (string, error) {
	return GlobalJWTManager.GenerateToken(user)
}

/**
 * 便捷函数：验证 Token
 */
func ValidateToken(tokenString string) (*Claims, error) {
	return GlobalJWTManager.ValidateToken(tokenString)
}

/**
 * 便捷函数：刷新 Token
 */
func RefreshToken(tokenString string) (string, error) {
	return GlobalJWTManager.RefreshToken(tokenString)
}

/**
 * 便捷函数：获取过期时间
 */
func GetTokenExpirationSeconds() int64 {
	return GlobalJWTManager.GetExpirationSeconds()
}
