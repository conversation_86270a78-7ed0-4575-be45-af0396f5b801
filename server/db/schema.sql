-- 勇远云联 Mesh VPN 数据库结构
-- 创建时间: 2025-06-14
-- 数据库: PostgreSQL

-- 创建数据库 (如果不存在)
-- CREATE DATABASE meshvpn;

-- 用户表
CREATE TABLE IF NOT EXISTS users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    phone VARCHAR(20),
    password_hash VARCHAR(255) NOT NULL,
    subscription_plan VARCHAR(20) DEFAULT 'free' CHECK (subscription_plan IN ('free', 'pro50', 'pro100', 'unlimited')),
    device_limit INTEGER DEFAULT 5,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 设备表
CREATE TABLE IF NOT EXISTS devices (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    device_uuid VARCHAR(36) UNIQUE NOT NULL,
    device_name VARCHAR(100) NOT NULL,
    mac_address VARCHAR(17),
    lan_ip INET,
    public_ip INET,
    wireguard_private_key TEXT,
    wireguard_public_key TEXT,
    interface_ip INET,
    last_heartbeat TIMESTAMP,
    status VARCHAR(20) DEFAULT 'offline' CHECK (status IN ('online', 'offline')),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 分组表
CREATE TABLE IF NOT EXISTS groups (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    group_name VARCHAR(100) NOT NULL,
    subnet CIDR NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, group_name)
);

-- 设备分组关联表 (多对多关系)
CREATE TABLE IF NOT EXISTS device_groups (
    device_id INTEGER NOT NULL REFERENCES devices(id) ON DELETE CASCADE,
    group_id INTEGER NOT NULL REFERENCES groups(id) ON DELETE CASCADE,
    joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (device_id, group_id)
);

-- 服务器表
CREATE TABLE IF NOT EXISTS servers (
    id SERIAL PRIMARY KEY,
    server_name VARCHAR(100) UNIQUE NOT NULL,
    public_endpoint VARCHAR(255) NOT NULL,
    public_key TEXT NOT NULL,
    region VARCHAR(50),
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'maintenance')),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引以提升查询性能
CREATE INDEX IF NOT EXISTS idx_devices_user_id ON devices(user_id);
CREATE INDEX IF NOT EXISTS idx_devices_device_uuid ON devices(device_uuid);
CREATE INDEX IF NOT EXISTS idx_devices_status ON devices(status);
CREATE INDEX IF NOT EXISTS idx_devices_last_heartbeat ON devices(last_heartbeat);
CREATE INDEX IF NOT EXISTS idx_groups_user_id ON groups(user_id);
CREATE INDEX IF NOT EXISTS idx_device_groups_device_id ON device_groups(device_id);
CREATE INDEX IF NOT EXISTS idx_device_groups_group_id ON device_groups(group_id);
CREATE INDEX IF NOT EXISTS idx_servers_status ON servers(status);

-- 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为需要自动更新 updated_at 的表创建触发器
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_devices_updated_at BEFORE UPDATE ON devices
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_groups_updated_at BEFORE UPDATE ON groups
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_servers_updated_at BEFORE UPDATE ON servers
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 插入默认服务器数据
INSERT INTO servers (server_name, public_endpoint, public_key, region) VALUES
('server-beijing', 'vpn-bj.yytcloud.net:51820', 'SERVER_PUBLIC_KEY_PLACEHOLDER', 'beijing'),
('server-shanghai', 'vpn-sh.yytcloud.net:51820', 'SERVER_PUBLIC_KEY_PLACEHOLDER', 'shanghai')
ON CONFLICT (server_name) DO NOTHING;

-- 创建用户设备数量检查函数
CREATE OR REPLACE FUNCTION check_device_limit()
RETURNS TRIGGER AS $$
DECLARE
    current_device_count INTEGER;
    user_device_limit INTEGER;
BEGIN
    -- 获取用户当前设备数量
    SELECT COUNT(*) INTO current_device_count
    FROM devices
    WHERE user_id = NEW.user_id;
    
    -- 获取用户设备限制
    SELECT device_limit INTO user_device_limit
    FROM users
    WHERE id = NEW.user_id;
    
    -- 检查是否超过限制 (-1 表示无限制)
    IF user_device_limit != -1 AND current_device_count >= user_device_limit THEN
        RAISE EXCEPTION '设备数量已达到订阅计划限制: %', user_device_limit;
    END IF;
    
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 创建设备数量限制触发器
CREATE TRIGGER check_device_limit_trigger
    BEFORE INSERT ON devices
    FOR EACH ROW EXECUTE FUNCTION check_device_limit();

-- 创建视图：用户设备统计
CREATE OR REPLACE VIEW user_device_stats AS
SELECT 
    u.id as user_id,
    u.username,
    u.subscription_plan,
    u.device_limit,
    COUNT(d.id) as current_device_count,
    COUNT(CASE WHEN d.status = 'online' THEN 1 END) as online_device_count
FROM users u
LEFT JOIN devices d ON u.id = d.user_id
GROUP BY u.id, u.username, u.subscription_plan, u.device_limit;

-- 创建视图：分组设备详情
CREATE OR REPLACE VIEW group_device_details AS
SELECT 
    g.id as group_id,
    g.group_name,
    g.subnet,
    g.user_id,
    d.id as device_id,
    d.device_name,
    d.device_uuid,
    d.interface_ip,
    d.status,
    d.last_heartbeat,
    dg.joined_at
FROM groups g
LEFT JOIN device_groups dg ON g.id = dg.group_id
LEFT JOIN devices d ON dg.device_id = d.id;

-- 注释说明
COMMENT ON TABLE users IS '用户信息表';
COMMENT ON TABLE devices IS '设备信息表';
COMMENT ON TABLE groups IS '分组信息表';
COMMENT ON TABLE device_groups IS '设备分组关联表';
COMMENT ON TABLE servers IS '服务器信息表';

COMMENT ON COLUMN users.subscription_plan IS '订阅计划: free(5设备), pro50(50设备), pro100(100设备), unlimited(无限)';
COMMENT ON COLUMN users.device_limit IS '设备数量限制, -1表示无限制';
COMMENT ON COLUMN devices.device_uuid IS '设备唯一标识符';
COMMENT ON COLUMN devices.status IS '设备状态: online, offline';
COMMENT ON COLUMN groups.subnet IS '分组子网段, 如 ********/24';
COMMENT ON COLUMN servers.status IS '服务器状态: active, maintenance';
