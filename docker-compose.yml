version: '3.8'

services:
  # PostgreSQL 数据库
  postgres:
    image: postgres:15-alpine
    container_name: meshvpn-postgres
    environment:
      POSTGRES_DB: meshvpn
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./server/db/schema.sql:/docker-entrypoint-initdb.d/01-schema.sql
    networks:
      - meshvpn-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d meshvpn"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis 缓存
  redis:
    image: redis:7-alpine
    container_name: meshvpn-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - meshvpn-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Mesh VPN 服务端
  server:
    build:
      context: .
      dockerfile: Dockerfile.server
    container_name: meshvpn-server
    environment:
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_USER=postgres
      - DB_PASSWORD=password
      - DB_NAME=meshvpn
      - DB_SSLMODE=disable
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=
      - JWT_SECRET=your_jwt_secret_key_change_in_production
      - SERVER_PORT=8080
      - GIN_MODE=release
    ports:
      - "8080:8080"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - meshvpn-network
    restart: unless-stopped
    volumes:
      - ./logs:/app/logs

  # 可选：数据库管理工具 pgAdmin
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: meshvpn-pgadmin
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin123
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    ports:
      - "5050:80"
    depends_on:
      - postgres
    networks:
      - meshvpn-network
    restart: unless-stopped
    profiles:
      - tools

  # 可选：Redis 管理工具
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: meshvpn-redis-commander
    environment:
      - REDIS_HOSTS=local:redis:6379
    ports:
      - "8081:8081"
    depends_on:
      - redis
    networks:
      - meshvpn-network
    restart: unless-stopped
    profiles:
      - tools

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local

networks:
  meshvpn-network:
    driver: bridge
