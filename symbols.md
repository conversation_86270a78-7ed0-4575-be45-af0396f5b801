# 符号命名登记表

## 数据库表名
| 名称 | 类型 | 所属模块 | 用途说明 | 示例 |
|------|------|----------|----------|------|
| users | 表名 | user | 用户信息表 | users |
| devices | 表名 | device | 设备信息表 | devices |
| groups | 表名 | group | 分组信息表 | groups |
| device_groups | 表名 | group | 设备分组关联表 | device_groups |
| servers | 表名 | server | 服务器信息表 | servers |

## Go 包名
| 名称 | 类型 | 所属模块 | 用途说明 | 示例 |
|------|------|----------|----------|------|
| main | 包名 | server | 服务端主程序 | package main |
| user | 包名 | server/internal | 用户管理模块 | package user |
| device | 包名 | server/internal | 设备管理模块 | package device |
| group | 包名 | server/internal | 分组管理模块 | package group |
| wireguard | 包名 | server/internal | WireGuard配置生成 | package wireguard |
| api | 包名 | server/internal | API路由层 | package api |
| auth | 包名 | client/internal | 客户端认证模块 | package auth |
| heartbeat | 包名 | client/internal | 心跳同步模块 | package heartbeat |
| nat | 包名 | client/internal | NAT穿透模块 | package nat |

## 结构体名称
| 名称 | 类型 | 所属模块 | 用途说明 | 示例 |
|------|------|----------|----------|------|
| User | 结构体 | user | 用户信息结构 | type User struct |
| Device | 结构体 | device | 设备信息结构 | type Device struct |
| Group | 结构体 | group | 分组信息结构 | type Group struct |
| Server | 结构体 | server | 服务器信息结构 | type Server struct |
| Config | 结构体 | config | 配置信息结构 | type Config struct |
| HeartbeatRequest | 结构体 | api | 心跳请求结构 | type HeartbeatRequest struct |
| LoginRequest | 结构体 | api | 登录请求结构 | type LoginRequest struct |
| RegisterRequest | 结构体 | api | 注册请求结构 | type RegisterRequest struct |

## 函数名称
| 名称 | 类型 | 所属模块 | 用途说明 | 示例 |
|------|------|----------|----------|------|
| CreateUser | 函数 | user | 创建用户 | func CreateUser(user *User) error |
| AuthenticateUser | 函数 | user | 用户认证 | func AuthenticateUser(username, password string) (*User, error) |
| RegisterDevice | 函数 | device | 注册设备 | func RegisterDevice(device *Device) error |
| UpdateHeartbeat | 函数 | device | 更新心跳 | func UpdateHeartbeat(deviceUUID string) error |
| CreateGroup | 函数 | group | 创建分组 | func CreateGroup(group *Group) error |
| GenerateWGConfig | 函数 | wireguard | 生成WG配置 | func GenerateWGConfig(device *Device) ([]byte, error) |
| EncryptConfig | 函数 | wireguard | 加密配置 | func EncryptConfig(data []byte, key string) ([]byte, error) |
| SendHeartbeat | 函数 | heartbeat | 发送心跳 | func SendHeartbeat(client *Client) error |
| ConnectToServer | 函数 | auth | 连接服务器 | func ConnectToServer(endpoint string) error |

## 常量名称
| 名称 | 类型 | 所属模块 | 用途说明 | 示例 |
|------|------|----------|----------|------|
| PlanFree | 常量 | user | 免费计划 | const PlanFree = "free" |
| PlanPro50 | 常量 | user | Pro50计划 | const PlanPro50 = "pro50" |
| PlanPro100 | 常量 | user | Pro100计划 | const PlanPro100 = "pro100" |
| PlanUnlimited | 常量 | user | 无限计划 | const PlanUnlimited = "unlimited" |
| DeviceStatusOnline | 常量 | device | 设备在线状态 | const DeviceStatusOnline = "online" |
| DeviceStatusOffline | 常量 | device | 设备离线状态 | const DeviceStatusOffline = "offline" |
| DefaultWGPort | 常量 | wireguard | 默认WG端口 | const DefaultWGPort = 51820 |
| HeartbeatInterval | 常量 | heartbeat | 心跳间隔 | const HeartbeatInterval = 5 * time.Second |

## 变量名称
| 名称 | 类型 | 所属模块 | 用途说明 | 示例 |
|------|------|----------|----------|------|
| db | 变量 | database | 数据库连接 | var db *gorm.DB |
| redisClient | 变量 | database | Redis客户端 | var redisClient *redis.Client |
| jwtSecret | 变量 | auth | JWT密钥 | var jwtSecret []byte |
| serverConfig | 变量 | config | 服务器配置 | var serverConfig *Config |
| clientConfig | 变量 | config | 客户端配置 | var clientConfig *ClientConfig |

## API 端点
| 名称 | 类型 | 所属模块 | 用途说明 | 示例 |
|------|------|----------|----------|------|
| /api/register | 端点 | api | 用户注册 | POST /api/register |
| /api/login | 端点 | api | 用户登录 | POST /api/login |
| /api/device/heartbeat | 端点 | api | 设备心跳 | POST /api/device/heartbeat |
| /api/device/config | 端点 | api | 获取设备配置 | GET /api/device/config |
| /api/device/group/update | 端点 | api | 更新设备分组 | POST /api/device/group/update |
| /api/peerlist | 端点 | api | 获取对等节点列表 | GET /api/peerlist |
| /api/version | 端点 | api | 检查版本更新 | GET /api/version |

## 配置文件字段
| 名称 | 类型 | 所属模块 | 用途说明 | 示例 |
|------|------|----------|----------|------|
| server_endpoints | 字段 | config | 服务器端点列表 | "server_endpoints": ["https://vpn1.example.com"] |
| device_name | 字段 | config | 设备名称 | "device_name": "my-laptop" |
| groups | 字段 | config | 分组配置 | "groups": [{"group_name": "home"}] |
| auth | 字段 | config | 认证信息 | "auth": {"username": "alice"} |
| database | 字段 | config | 数据库配置 | "database": {"host": "localhost"} |
| redis | 字段 | config | Redis配置 | "redis": {"host": "localhost"} |
| wireguard | 字段 | config | WireGuard配置 | "wireguard": {"port": 51820} |

## 环境变量
| 名称 | 类型 | 所属模块 | 用途说明 | 示例 |
|------|------|----------|----------|------|
| DB_HOST | 环境变量 | database | 数据库主机 | DB_HOST=localhost |
| DB_PORT | 环境变量 | database | 数据库端口 | DB_PORT=5432 |
| DB_NAME | 环境变量 | database | 数据库名称 | DB_NAME=meshvpn |
| DB_USER | 环境变量 | database | 数据库用户 | DB_USER=postgres |
| DB_PASSWORD | 环境变量 | database | 数据库密码 | DB_PASSWORD=password |
| REDIS_HOST | 环境变量 | redis | Redis主机 | REDIS_HOST=localhost |
| REDIS_PORT | 环境变量 | redis | Redis端口 | REDIS_PORT=6379 |
| JWT_SECRET | 环境变量 | auth | JWT密钥 | JWT_SECRET=your_secret_key |
| SERVER_PORT | 环境变量 | server | 服务器端口 | SERVER_PORT=8080 |
