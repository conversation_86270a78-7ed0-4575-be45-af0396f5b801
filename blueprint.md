# 系统蓝图 - 组件登记簿

## 数据库表结构

### users 表
| 字段名 | 类型 | 说明 | 示例 |
|--------|------|------|------|
| id | SERIAL PRIMARY KEY | 用户ID | 1 |
| username | VARCHAR(50) UNIQUE | 用户名 | "alice" |
| email | VARCHAR(100) UNIQUE | 邮箱 | "<EMAIL>" |
| phone | VARCHAR(20) | 手机号 | "+86138****1234" |
| password_hash | VARCHAR(255) | 密码哈希 | bcrypt hash |
| subscription_plan | VARCHAR(20) | 订阅计划 | "free", "pro50", "pro100", "unlimited" |
| device_limit | INTEGER | 设备限制 | 5, 50, 100, -1 |
| created_at | TIMESTAMP | 创建时间 | 2025-06-14 10:00:00 |
| updated_at | TIMESTAMP | 更新时间 | 2025-06-14 10:00:00 |

### devices 表
| 字段名 | 类型 | 说明 | 示例 |
|--------|------|------|------|
| id | SERIAL PRIMARY KEY | 设备ID | 1 |
| user_id | INTEGER REFERENCES users(id) | 所属用户 | 1 |
| device_uuid | VARCHAR(36) UNIQUE | 设备唯一标识 | "550e8400-e29b-41d4-a716-************" |
| device_name | VARCHAR(100) | 设备名称 | "laptop-work" |
| mac_address | VARCHAR(17) | MAC地址 | "00:11:22:33:44:55" |
| lan_ip | INET | 局域网IP | "*************" |
| public_ip | INET | 公网IP | "***********" |
| wireguard_private_key | TEXT | WG私钥 | base64 encoded |
| wireguard_public_key | TEXT | WG公钥 | base64 encoded |
| interface_ip | INET | 接口IP | "********/24" |
| last_heartbeat | TIMESTAMP | 最后心跳 | 2025-06-14 10:00:00 |
| status | VARCHAR(20) | 设备状态 | "online", "offline" |
| created_at | TIMESTAMP | 创建时间 | 2025-06-14 10:00:00 |

### groups 表
| 字段名 | 类型 | 说明 | 示例 |
|--------|------|------|------|
| id | SERIAL PRIMARY KEY | 分组ID | 1 |
| user_id | INTEGER REFERENCES users(id) | 所属用户 | 1 |
| group_name | VARCHAR(100) | 分组名称 | "home-network" |
| subnet | CIDR | 子网段 | "********/24" |
| created_at | TIMESTAMP | 创建时间 | 2025-06-14 10:00:00 |

### device_groups 表 (多对多关系)
| 字段名 | 类型 | 说明 | 示例 |
|--------|------|------|------|
| device_id | INTEGER REFERENCES devices(id) | 设备ID | 1 |
| group_id | INTEGER REFERENCES groups(id) | 分组ID | 1 |
| joined_at | TIMESTAMP | 加入时间 | 2025-06-14 10:00:00 |

### servers 表
| 字段名 | 类型 | 说明 | 示例 |
|--------|------|------|------|
| id | SERIAL PRIMARY KEY | 服务器ID | 1 |
| server_name | VARCHAR(100) | 服务器名称 | "server-beijing" |
| public_endpoint | VARCHAR(255) | 公网端点 | "vpn.example.com:51820" |
| public_key | TEXT | 服务器公钥 | base64 encoded |
| region | VARCHAR(50) | 地区 | "beijing", "shanghai" |
| status | VARCHAR(20) | 状态 | "active", "maintenance" |
| created_at | TIMESTAMP | 创建时间 | 2025-06-14 10:00:00 |

## API 接口字段规范

### 用户注册 POST /api/register
**请求字段：**
- username: string (用户名)
- password: string (密码)
- email: string (邮箱)
- phone: string (手机号，可选)

**响应字段：**
- success: boolean (是否成功)
- message: string (消息)
- user_id: integer (用户ID)

### 用户登录 POST /api/login
**请求字段：**
- username: string (用户名)
- password: string (密码)

**响应字段：**
- success: boolean (是否成功)
- token: string (JWT token)
- expires_in: integer (过期时间秒数)

### 设备心跳 POST /api/device/heartbeat
**请求字段：**
- device_uuid: string (设备UUID)
- device_name: string (设备名称)
- mac_address: string (MAC地址)
- lan_ip: string (局域网IP)
- public_ip: string (公网IP)
- route1: string (路由1，可选)
- route2: string (路由2，可选)

**响应字段：**
- success: boolean (是否成功)
- config_updated: boolean (配置是否更新)
- peers: array (对等节点列表)

## Go 结构体定义

### User 结构体
```go
type User struct {
    ID               uint      `json:"id" gorm:"primaryKey"`
    Username         string    `json:"username" gorm:"uniqueIndex;size:50"`
    Email            string    `json:"email" gorm:"uniqueIndex;size:100"`
    Phone            string    `json:"phone" gorm:"size:20"`
    PasswordHash     string    `json:"-" gorm:"size:255"`
    SubscriptionPlan string    `json:"subscription_plan" gorm:"size:20;default:free"`
    DeviceLimit      int       `json:"device_limit" gorm:"default:5"`
    CreatedAt        time.Time `json:"created_at"`
    UpdatedAt        time.Time `json:"updated_at"`
}
```

### Device 结构体
```go
type Device struct {
    ID                   uint      `json:"id" gorm:"primaryKey"`
    UserID               uint      `json:"user_id"`
    DeviceUUID           string    `json:"device_uuid" gorm:"uniqueIndex;size:36"`
    DeviceName           string    `json:"device_name" gorm:"size:100"`
    MacAddress           string    `json:"mac_address" gorm:"size:17"`
    LanIP                string    `json:"lan_ip"`
    PublicIP             string    `json:"public_ip"`
    WireguardPrivateKey  string    `json:"-" gorm:"type:text"`
    WireguardPublicKey   string    `json:"wireguard_public_key" gorm:"type:text"`
    InterfaceIP          string    `json:"interface_ip"`
    LastHeartbeat        time.Time `json:"last_heartbeat"`
    Status               string    `json:"status" gorm:"size:20;default:offline"`
    CreatedAt            time.Time `json:"created_at"`
}
```

## 常量定义

### 订阅计划
```go
const (
    PlanFree      = "free"      // 免费版：5设备
    PlanPro50     = "pro50"     // Pro50：50设备/年 ¥99
    PlanPro100    = "pro100"    // Pro100：100设备/年 ¥188
    PlanUnlimited = "unlimited" // 无限版：无限设备/年 ¥388
)
```

### 设备状态
```go
const (
    DeviceStatusOnline  = "online"
    DeviceStatusOffline = "offline"
)
```

### 服务器状态
```go
const (
    ServerStatusActive      = "active"
    ServerStatusMaintenance = "maintenance"
)
```

## 配置文件结构

### 客户端配置 (config.json)
```json
{
  "server_endpoints": [
    "https://vpn1.example.com",
    "https://vpn2.example.com"
  ],
  "device_name": "my-laptop",
  "groups": [
    {
      "group_name": "home-network",
      "members": ["laptop", "phone", "tablet"]
    }
  ],
  "auth": {
    "username": "alice",
    "token": "jwt_token_here"
  }
}
```

### 服务端配置 (server.yaml)
```yaml
server:
  port: 8080
  host: "0.0.0.0"
database:
  host: "localhost"
  port: 5432
  name: "meshvpn"
  user: "postgres"
  password: "password"
redis:
  host: "localhost"
  port: 6379
  password: ""
wireguard:
  interface: "wg0"
  port: 51820
  subnet: "10.0.0.0/16"
```
