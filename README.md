# 勇远云联 Mesh VPN 系统

基于 WireGuard 的跨平台 Mesh VPN 解决方案，支持自动化组网、设备管理、分组控制和多平台客户端。

## 🚀 项目特性

### 核心功能
- **用户管理**: 注册/登录/JWT认证/订阅计划管理
- **设备管理**: 自动注册/实时心跳/状态监控/密钥管理
- **分组管理**: 设备分组/子网管理/权限控制
- **配置生成**: WireGuard 配置自动生成/AES256 加密
- **多平台支持**: Linux/Windows/macOS/Android

### 技术架构
- **后端**: Golang + Gin + PostgreSQL + Redis
- **前端**: CLI (已完成) + <PERSON><PERSON> (桌面) + Flutter (移动端)
- **加密**: WireGuard + AES256 + JWT
- **部署**: Docker + Docker Compose

## 📁 项目结构

```
wireguard/
├── server/                 # 服务端代码
│   ├── cmd/               # 主程序入口
│   ├── internal/          # 内部模块
│   │   ├── api/          # API路由层
│   │   ├── user/         # 用户管理
│   │   ├── device/       # 设备管理
│   │   ├── group/        # 分组管理
│   │   ├── wireguard/    # WG配置生成
│   │   └── database/     # 数据库连接
│   └── db/               # 数据库脚本
├── client/                # 客户端代码
│   ├── cli/              # CLI客户端
│   ├── gui/              # GUI客户端
│   └── internal/         # 内部模块
├── deploy/               # 部署脚本
└── docs/                 # 文档
```

## 🛠️ 快速开始

### 环境要求
- Go 1.23+
- PostgreSQL 15+
- Redis 7+
- Docker (可选)

### 安装依赖
```bash
# 安装 Go 依赖
go mod tidy

# 安装系统依赖 (Ubuntu/Debian)
sudo apt update
sudo apt install -y postgresql postgresql-contrib redis-server
```

### 数据库初始化
```bash
# 创建数据库
sudo -u postgres createdb meshvpn

# 初始化表结构
sudo -u postgres psql -d meshvpn -f server/db/schema.sql
```

### 启动服务端
```bash
# 编译服务端
go build -o bin/server ./server/cmd/main.go

# 启动服务端
./bin/server
```

### 启动客户端
```bash
# 编译客户端
go build -o bin/client ./client/cli/main.go

# 用户注册
echo -e "username\npassword\<EMAIL>" | ./bin/client --register

# 用户登录
echo -e "username\npassword" | ./bin/client --login

# 启动心跳服务
./bin/client
```

## 🔧 配置说明

### 服务端环境变量
```bash
export DB_HOST=localhost
export DB_PORT=5432
export DB_USER=postgres
export DB_PASSWORD=password
export DB_NAME=meshvpn
export REDIS_HOST=localhost
export REDIS_PORT=6379
export JWT_SECRET=your_secret_key
export SERVER_PORT=8080
```

### 客户端配置文件 (config.json)
```json
{
  "server_endpoints": [
    "http://localhost:8080"
  ],
  "device_name": "my-device",
  "device_uuid": "auto-generated",
  "heartbeat": {
    "interval": 5,
    "enabled": true
  },
  "wireguard": {
    "interface": "wg0",
    "port": 51820,
    "auto_connect": true
  }
}
```

## 📊 API 接口

### 认证接口
- `POST /api/auth/register` - 用户注册
- `POST /api/auth/login` - 用户登录
- `POST /api/auth/refresh` - 刷新Token

### 用户管理
- `GET /api/user/profile` - 获取用户信息
- `PUT /api/user/profile` - 更新用户信息
- `GET /api/user/stats` - 获取用户统计

### 设备管理
- `POST /api/device/register` - 注册设备
- `POST /api/device/heartbeat` - 设备心跳
- `GET /api/device/list` - 获取设备列表
- `GET /api/device/:uuid` - 获取设备详情

### 分组管理
- `POST /api/group` - 创建分组
- `GET /api/group/list` - 获取分组列表
- `GET /api/group/:id` - 获取分组详情
- `POST /api/group/:id/devices` - 添加设备到分组

## 🧪 测试

### 运行系统测试
```bash
# 运行完整系统测试
./test_system.sh
```

### 手动测试
```bash
# 健康检查
curl http://localhost:8080/api/health

# 用户注册
curl -X POST http://localhost:8080/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{"username":"test","password":"test123","email":"<EMAIL>"}'

# 用户登录
curl -X POST http://localhost:8080/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"test","password":"test123"}'
```

## 📈 订阅计划

| 计划 | 设备限制 | 价格 | 功能 |
|------|----------|------|------|
| 免费版 | 5台设备 | 免费 | 基础功能 |
| Pro50 | 50台设备 | ¥99/年 | 高级功能 |
| Pro100 | 100台设备 | ¥188/年 | 企业功能 |
| 无限版 | 无限制 | ¥388/年 | 全部功能 |

## 🔒 安全特性

- **JWT认证**: 24小时过期，支持刷新
- **密码加密**: bcrypt 哈希存储
- **配置加密**: AES256 加密 WireGuard 配置
- **HTTPS通信**: 强制使用 HTTPS
- **输入验证**: 严格的API参数验证

## 🚀 部署

### Docker 部署
```bash
# 启动所有服务
docker-compose up -d

# 仅启动数据库
docker-compose up -d postgres redis

# 查看日志
docker-compose logs -f
```

### 生产环境
```bash
# 构建生产版本
CGO_ENABLED=0 GOOS=linux go build -ldflags="-w -s" -o server ./server/cmd/main.go

# 使用 systemd 管理服务
sudo cp deploy/meshvpn.service /etc/systemd/system/
sudo systemctl enable meshvpn
sudo systemctl start meshvpn
```

## 📝 开发进度

- ✅ 用户管理系统
- ✅ 设备管理系统  
- ✅ 分组管理系统
- ✅ WireGuard 配置生成
- ✅ CLI 客户端
- ⏳ NAT 穿透功能
- ⏳ Tauri 桌面客户端
- ⏳ Flutter 移动客户端
- ⏳ 自动更新机制

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📄 许可证

MIT License

## 📞 联系方式

- 项目地址: https://github.com/yytcloud/meshvpn
- 技术支持: <EMAIL>
- 官方网站: https://yytcloud.net

---

**勇远云联 Mesh VPN** - 让网络连接更简单、更安全、更智能！
