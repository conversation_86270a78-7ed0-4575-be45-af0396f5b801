Mesh VPN System Specification Document

Project Name

勇远云联 (Yongyuan Cloud MeshVPN)

Project Description

A full-mesh, cross-platform VPN system built on WireGuard, supporting NAT traversal, device grouping, multi-server federation, and subscription-based device limits. Designed for seamless zero-config user experience with CLI and GUI clients.

⸻

1. Architecture Overview

Components
	•	Client Agent (Linux/macOS/Windows/Android)
	•	CLI and GUI modes
	•	Supports device registration, login, group management, auto config, NAT punching
	•	Control Server (multi-instance)
	•	HTTP + WebSocket API
	•	Generates encrypted WireGuard configs
	•	Manages users/devices/groups
	•	Relay Server (optional)
	•	UDP relay fallback
	•	Database
	•	PostgreSQL (main data store)
	•	Redis (state/cache)

⸻

2. Functional Modules

2.1 User Management
	•	Registration (username, password, email, phone)
	•	Login with JWT token issuance
	•	Subscription plan:
	•	Free: 5 devices
	•	Pro50: 50 devices/year ¥99
	•	Pro100: 100 devices/year ¥188
	•	Unlimited: Unlimited devices/year ¥388

2.2 Device Registration
	•	On first startup, client generates and persists unique device_uuid
	•	Devices upload device_name, mac, lan_ip, public_ip, group_membership
	•	Enforced device quota per user plan

2.3 Group Management
	•	Each user can define multiple groups
	•	One device can belong to multiple groups
	•	Group membership defines which devices are interconnected
	•	Group definitions:
	•	CLI: via JSON config file
	•	GUI: via drag/drop or checkbox interface
	•	Group config uploaded to server to regenerate topology

2.4 WireGuard Config Generation
	•	Server generates wg0.conf and full peer list for each device
	•	Configs include:
	•	Private key
	•	Interface IP (10.x.x.x/24 per group)
	•	Peer list with allowed IPs
	•	Packaged into encrypted .tar.gz with AES

2.5 NAT Traversal
	•	Server collects client-reported public/private IPs + ports
	•	Server distributes peer connection list to each device
	•	Client connects in order of preference:
	•	LAN IP
	•	Public IP
	•	Relay fallback (UDP relay)
	•	Supported techniques:
	•	UDP Hole Punching
	•	STUN server
	•	Relay server
	•	Future: UPnP / NAT-PMP / ICE-style selection

2.6 Heartbeat System
	•	Every 5s, client sends WebSocket heartbeat with:
	•	device_uuid, client_name, mac, lan_ip, public_ip, route1, route2
	•	Server updates device last-seen and status

2.7 Auto Update & Startup
	•	Client self-updates from /api/version
	•	On config change or new version, restarts with new binary
	•	Supports platform-specific auto-start:
	•	Linux: systemd
	•	macOS: launchctl
	•	Windows: registry/startup folder
	•	Android: background service

⸻

3. Technical Stack

Server
	•	Language: Golang
	•	API: REST + WebSocket
	•	Database: PostgreSQL + Redis
	•	Encryption: AES256 for config tarballs
	•	Deployment: Docker Compose, optional multi-server sync via pglogical or LiteFS

Client
	•	Language: Go (core) + Flutter (GUI) / Tauri (desktop)
	•	WireGuard integration:
	•	Linux/macOS: wg-quick + direct wg commands
	•	Windows: WireGuardNT driver
	•	Android: WireGuard SDK

⸻

4. Key APIs (HTTP)

Method	Endpoint	Description
POST	/api/register	Register new user
POST	/api/login	Authenticate user
POST	/api/device/heartbeat	Send heartbeat info
GET	/api/device/config	Download encrypted config
POST	/api/device/group/update	Upload group config
GET	/api/peerlist	Get peer endpoint list
GET	/api/version	Check for client update
POST	/api/upgrade/status	Notify upgrade result


⸻

5. Client Config Structure (CLI Mode)

{
  "device_name": "sensor-1",
  "groups": [
    {
      "group_name": "iot-net",
      "members": ["sensor-1", "sensor-2", "gateway"]
    }
  ]
}


⸻

6. Development Phases

Phase 1: MVP (1-2 weeks)
	•	CLI agent (Linux first)
	•	Single control server
	•	Basic WireGuard config downlink
	•	Mesh connection inside single group

Phase 2: Multi-server + NAT Enhancements (1-2 weeks)
	•	Client server-selection via ping
	•	Cross-server sync (pglogical or API driven)
	•	Add STUN, relay fallback

Phase 3: GUI Clients + Auto Update (1-2 weeks)
	•	GUI (Tauri desktop / Flutter mobile)
	•	Self-update mechanism

Phase 4: Billing & Productionization (1-2 weeks)
	•	Subscription enforcement
	•	Plan upgrades, alerts
	•	Relay server deployment

⸻

7. Deliverables
	•	CLI + GUI clients (Go + Tauri/Flutter)
	•	Configurable control server (Go + PostgreSQL)
	•	Encrypted config generator
	•	Relay server (UDP)
	•	Deployment scripts
	•	Full documentation: API, architecture, install guides