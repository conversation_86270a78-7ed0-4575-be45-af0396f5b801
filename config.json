{"server_endpoints": ["http://localhost:8080"], "device_name": "asus", "device_uuid": "7249e911-4e23-4ac4-93e3-4f775a483322", "groups": [{"group_name": "default", "members": ["asus"]}], "auth": {"username": "bob", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoyLCJ1c2VybmFtZSI6ImJvYiIsImlzcyI6Im1lc2h2cG4iLCJzdWIiOiIyIiwiZXhwIjoxNzQ5OTY5OTUyLCJuYmYiOjE3NDk4ODM1NTIsImlhdCI6MTc0OTg4MzU1Mn0.TG7du_yrM2Niwsdhf-FRdO0Lm451HPi0POfvppmlh_o", "user_id": 2}, "heartbeat": {"interval": 5, "enabled": true}, "wireguard": {"interface": "wg0", "port": 51820, "config_path": "/etc/wireguard/wg0.conf", "auto_connect": true}}